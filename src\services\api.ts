import { globalMonitor } from '../hooks/usePerformanceMonitor';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface Enterprise {
  id: string;
  name: string;
  industry: string;
  size?: string;
  stage?: string;
  location?: string;
  revenue?: string;
  employees?: number;
  founded?: number;
  status?: string;
  score?: number;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface EnterpriseQuery {
  page?: number;
  limit?: number;
  industry?: string;
  size?: string;
  stage?: string;
  location?: string;
  search?: string;
}

export interface AIAnalysisRequest {
  companyName: string;
  industry?: string;
  description?: string;
  targetConditions?: {
    industry?: string;
    size?: string;
    stage?: string;
    location?: string;
  };
}

class ApiService {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const startTime = performance.now();
    const timestamp = Date.now();

    try {
      const url = `${API_BASE_URL}${endpoint}`;
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      // Record successful API call
      const duration = performance.now() - startTime;
      globalMonitor.addMetric({
        name: `API:${endpoint}`,
        duration,
        timestamp,
        success: true
      });

      return data;
    } catch (error) {
      console.error('API request failed:', error);

      // Record failed API call
      const duration = performance.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      globalMonitor.addMetric({
        name: `API:${endpoint}`,
        duration,
        timestamp,
        success: false,
        error: errorMessage
      });

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  // Health check
  async healthCheck(): Promise<ApiResponse> {
    return this.request('/health');
  }

  // Enterprise APIs
  async getEnterprises(query: EnterpriseQuery = {}): Promise<ApiResponse<{
    enterprises: Enterprise[];
    pagination: any;
  }>> {
    const searchParams = new URLSearchParams();
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, String(value));
      }
    });

    const queryString = searchParams.toString();
    return this.request(`/api/enterprises${queryString ? `?${queryString}` : ''}`);
  }

  async getEnterprise(id: string): Promise<ApiResponse<Enterprise>> {
    return this.request(`/api/enterprises/${id}`);
  }

  async createEnterprise(enterprise: Partial<Enterprise>): Promise<ApiResponse<Enterprise>> {
    return this.request('/api/enterprises', {
      method: 'POST',
      body: JSON.stringify(enterprise),
    });
  }

  async getEnterpriseStats(): Promise<ApiResponse> {
    return this.request('/api/enterprises/stats/overview');
  }

  // AI APIs
  async analyzeEnterprise(request: AIAnalysisRequest): Promise<ApiResponse> {
    return this.request('/api/ai/analyze-enterprise', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async generateMatching(enterprises: any[], targetConditions: any): Promise<ApiResponse> {
    return this.request('/api/ai/generate-matching', {
      method: 'POST',
      body: JSON.stringify({ enterprises, targetConditions }),
    });
  }

  async analyzePathway(enterpriseData: any, roleData: any): Promise<ApiResponse> {
    return this.request('/api/ai/analyze-pathway', {
      method: 'POST',
      body: JSON.stringify({ enterpriseData, roleData }),
    });
  }

  async getAIStatus(): Promise<ApiResponse> {
    return this.request('/api/ai/status');
  }

  // 智能分析APIs
  async performIntelligentAnalysis(data: {
    enterpriseId: string;
    analysisTypes: string[];
    includeCompetitors?: boolean;
    depth?: string;
  }): Promise<ApiResponse> {
    return this.request('/api/ai/intelligent-analysis', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getAnalysisHistory(enterpriseId: string, analysisType?: string): Promise<ApiResponse> {
    const params = new URLSearchParams();
    if (analysisType) params.append('analysisType', analysisType);

    return this.request(`/api/ai/analysis-history/${enterpriseId}${params.toString() ? `?${params.toString()}` : ''}`);
  }

  // 图像生成APIs
  async generateVisualization(data: {
    enterpriseName: string;
    industry: string;
    stage: string;
    analysisData?: any;
    visualizationType: string;
  }): Promise<ApiResponse> {
    return this.request('/api/ai/generate-visualization', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async generateBatchVisualization(data: {
    enterpriseName: string;
    industry: string;
    stage: string;
    analysisData?: any;
  }): Promise<ApiResponse> {
    return this.request('/api/ai/generate-batch-visualization', {
      method: 'POST',
      body: JSON.stringify({
        ...data,
        visualizationType: 'logo_concept' // 批量生成会忽略这个参数
      }),
    });
  }

  // Analysis APIs
  async createProfile(data: {
    targetConditions: any;
    searchMethods?: any;
  }): Promise<ApiResponse> {
    return this.request('/api/analysis/profile', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getEnterpriseAnalysis(id: string): Promise<ApiResponse> {
    return this.request(`/api/analysis/enterprise/${id}`);
  }

  async getRoleAnalysis(enterpriseId: string): Promise<ApiResponse> {
    return this.request(`/api/analysis/roles/${enterpriseId}`);
  }

  async getPathwayAnalysis(enterpriseId: string): Promise<ApiResponse> {
    return this.request(`/api/analysis/pathway/${enterpriseId}`);
  }

  // 可视化APIs
  async getNetworkData(params?: { industry?: string; limit?: number }): Promise<ApiResponse> {
    const searchParams = new URLSearchParams();
    if (params?.industry) searchParams.append('industry', params.industry);
    if (params?.limit) searchParams.append('limit', params.limit.toString());

    return this.request(`/api/visualization/network-data${searchParams.toString() ? `?${searchParams.toString()}` : ''}`);
  }

  async getTrendsData(params?: { period?: string; metric?: string }): Promise<ApiResponse> {
    const searchParams = new URLSearchParams();
    if (params?.period) searchParams.append('period', params.period);
    if (params?.metric) searchParams.append('metric', params.metric);

    return this.request(`/api/visualization/trends${searchParams.toString() ? `?${searchParams.toString()}` : ''}`);
  }

  async getAnalyticsSummary(): Promise<ApiResponse> {
    return this.request('/api/visualization/analytics-summary');
  }

  // Settings APIs
  async getSystemSettings(): Promise<ApiResponse> {
    return this.request('/api/settings');
  }

  async updateSystemSettings(settings: any): Promise<ApiResponse> {
    return this.request('/api/settings', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  }

  async testDatabaseConnection(dbType: string): Promise<ApiResponse> {
    return this.request('/api/settings/test-connection', {
      method: 'POST',
      body: JSON.stringify({ dbType }),
    });
  }

  async backupDatabase(): Promise<ApiResponse> {
    return this.request('/api/settings/backup', {
      method: 'POST',
    });
  }

  async getSystemInfo(): Promise<ApiResponse> {
    return this.request('/api/settings/system-info');
  }

  // Profile Analysis APIs
  async createProfileAnalysis(enterpriseData: any, analysisType?: string): Promise<ApiResponse> {
    return this.request('/api/analysis/profile/create', {
      method: 'POST',
      body: JSON.stringify({ enterpriseData, analysisType }),
    });
  }

  async updateProfileStep(profileId: string, stepNumber: number, stepData: any, isCompleted: boolean = false): Promise<ApiResponse> {
    return this.request(`/api/analysis/profile/${profileId}/step`, {
      method: 'PUT',
      body: JSON.stringify({ stepNumber, stepData, isCompleted }),
    });
  }

  async getProfileProgress(profileId: string): Promise<ApiResponse> {
    return this.request(`/api/analysis/profile/${profileId}/progress`);
  }

  async completeProfileAnalysis(profileId: string): Promise<ApiResponse> {
    return this.request(`/api/analysis/profile/${profileId}/complete`, {
      method: 'POST',
    });
  }

  async saveProfileDraft(profileId: string, draftData: any): Promise<ApiResponse> {
    return this.request(`/api/analysis/profile/${profileId}/draft`, {
      method: 'POST',
      body: JSON.stringify({ draftData }),
    });
  }

  async getProfileDraft(profileId: string): Promise<ApiResponse> {
    return this.request(`/api/analysis/profile/${profileId}/draft`);
  }

  // Report APIs
  async getReportTemplates(): Promise<ApiResponse> {
    return this.request('/api/reports/templates');
  }

  async getReportTemplate(templateId: string): Promise<ApiResponse> {
    return this.request(`/api/reports/templates/${templateId}`);
  }

  async createReportTemplate(template: any): Promise<ApiResponse> {
    return this.request('/api/reports/templates', {
      method: 'POST',
      body: JSON.stringify(template),
    });
  }

  async updateReportTemplate(templateId: string, template: any): Promise<ApiResponse> {
    return this.request(`/api/reports/templates/${templateId}`, {
      method: 'PUT',
      body: JSON.stringify(template),
    });
  }

  async deleteReportTemplate(templateId: string): Promise<ApiResponse> {
    return this.request(`/api/reports/templates/${templateId}`, {
      method: 'DELETE',
    });
  }

  async generateReport(request: any): Promise<ApiResponse> {
    return this.request('/api/reports/generate', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async getReportStatus(reportId: string): Promise<ApiResponse> {
    return this.request(`/api/reports/${reportId}/status`);
  }

  async downloadReport(reportId: string): Promise<Blob> {
    const response = await fetch(`${this.baseUrl}/api/reports/${reportId}/download`, {
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      throw new Error('Download failed');
    }

    return response.blob();
  }
}

export const apiService = new ApiService();
