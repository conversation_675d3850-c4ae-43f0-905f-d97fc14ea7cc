export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: 'enterprise_profile' | 'role_analysis' | 'pathway_analysis' | 'comprehensive';
  version: string;
  createdAt: string;
  updatedAt: string;
  isDefault: boolean;
  sections: ReportSection[];
  metadata: ReportMetadata;
}

export interface ReportSection {
  id: string;
  title: string;
  type: 'text' | 'chart' | 'table' | 'image' | 'list' | 'metrics' | 'swot';
  order: number;
  required: boolean;
  config: SectionConfig;
  dataSource?: string;
}

export interface SectionConfig {
  // Text section
  content?: string;
  fontSize?: number;
  alignment?: 'left' | 'center' | 'right';
  
  // Chart section
  chartType?: 'pie' | 'bar' | 'line' | 'radar' | 'scatter';
  chartConfig?: any;
  
  // Table section
  columns?: TableColumn[];
  showHeader?: boolean;
  
  // Image section
  imageUrl?: string;
  width?: number;
  height?: number;
  
  // List section
  listType?: 'bullet' | 'numbered';
  items?: string[];
  
  // Metrics section
  metrics?: MetricConfig[];
  
  // SWOT section
  swotData?: {
    strengths: string[];
    weaknesses: string[];
    opportunities: string[];
    threats: string[];
  };
}

export interface TableColumn {
  key: string;
  title: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  format?: 'text' | 'number' | 'currency' | 'percentage' | 'date';
}

export interface MetricConfig {
  label: string;
  value: string | number;
  unit?: string;
  trend?: {
    value: number;
    direction: 'up' | 'down' | 'stable';
  };
  color?: string;
}

export interface ReportMetadata {
  author: string;
  company: string;
  logo?: string;
  footer?: string;
  pageSize: 'A4' | 'A3' | 'Letter';
  orientation: 'portrait' | 'landscape';
  margins: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  theme: {
    primaryColor: string;
    secondaryColor: string;
    fontFamily: string;
    fontSize: number;
  };
}

export interface ReportData {
  templateId: string;
  enterpriseId: string;
  enterpriseName: string;
  generatedAt: string;
  generatedBy: string;
  data: Record<string, any>;
  sections: ReportSectionData[];
}

export interface ReportSectionData {
  sectionId: string;
  title: string;
  content: any;
  charts?: ChartData[];
  tables?: TableData[];
  images?: ImageData[];
}

export interface ChartData {
  type: string;
  data: any;
  options?: any;
}

export interface TableData {
  headers: string[];
  rows: any[][];
}

export interface ImageData {
  url: string;
  caption?: string;
  width?: number;
  height?: number;
}

export interface ReportExportOptions {
  format: 'pdf' | 'excel' | 'word' | 'html';
  includeCharts: boolean;
  includeTables: boolean;
  includeImages: boolean;
  watermark?: string;
  password?: string;
  compression?: boolean;
}

export interface ReportGenerationRequest {
  templateId: string;
  enterpriseId: string;
  dataOverrides?: Record<string, any>;
  exportOptions: ReportExportOptions;
  customSections?: ReportSection[];
}

export interface ReportGenerationResponse {
  success: boolean;
  reportId: string;
  downloadUrl?: string;
  error?: string;
  metadata: {
    fileSize: number;
    pageCount?: number;
    generationTime: number;
  };
}

// Default templates
export const DEFAULT_TEMPLATES: Partial<ReportTemplate>[] = [
  {
    name: '企业画像分析报告',
    description: '全面的企业画像分析报告，包含基本信息、行业分析、竞争分析等',
    type: 'enterprise_profile',
    isDefault: true,
    sections: [
      {
        id: 'executive_summary',
        title: '执行摘要',
        type: 'text',
        order: 1,
        required: true,
        config: { fontSize: 14, alignment: 'left' }
      },
      {
        id: 'basic_info',
        title: '企业基本信息',
        type: 'table',
        order: 2,
        required: true,
        config: {
          columns: [
            { key: 'field', title: '字段', width: 30 },
            { key: 'value', title: '值', width: 70 }
          ],
          showHeader: true
        }
      },
      {
        id: 'industry_analysis',
        title: '行业分析',
        type: 'chart',
        order: 3,
        required: true,
        config: { chartType: 'pie' }
      },
      {
        id: 'swot_analysis',
        title: 'SWOT分析',
        type: 'swot',
        order: 4,
        required: true,
        config: {}
      },
      {
        id: 'recommendations',
        title: '建议与结论',
        type: 'list',
        order: 5,
        required: true,
        config: { listType: 'bullet' }
      }
    ]
  },
  {
    name: '角色分析报告',
    description: '企业决策角色分析报告',
    type: 'role_analysis',
    isDefault: true,
    sections: [
      {
        id: 'role_overview',
        title: '角色概览',
        type: 'metrics',
        order: 1,
        required: true,
        config: {}
      },
      {
        id: 'decision_flow',
        title: '决策流程',
        type: 'chart',
        order: 2,
        required: true,
        config: { chartType: 'bar' }
      },
      {
        id: 'role_details',
        title: '角色详情',
        type: 'table',
        order: 3,
        required: true,
        config: {
          columns: [
            { key: 'role', title: '角色', width: 25 },
            { key: 'responsibility', title: '职责', width: 35 },
            { key: 'influence', title: '影响力', width: 20 },
            { key: 'contact', title: '联系方式', width: 20 }
          ]
        }
      }
    ]
  }
];
