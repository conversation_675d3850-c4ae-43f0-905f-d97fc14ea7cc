import { useState, useEffect, useCallback, createContext, useContext } from 'react';
import { User, AuthState, LoginRequest, LoginResponse, Permission, PermissionName } from '../types/auth';
import { apiService } from '../services/api';

interface AuthContextType extends AuthState {
  login: (credentials: LoginRequest) => Promise<boolean>;
  logout: () => void;
  refreshAuth: () => Promise<boolean>;
  hasPermission: (permission: PermissionName, resource?: any) => boolean;
  hasRole: (roleName: string) => boolean;
  hasAnyPermission: (permissions: PermissionName[]) => boolean;
  hasAllPermissions: (permissions: PermissionName[]) => boolean;
  updateUserPreferences: (preferences: Partial<User['preferences']>) => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const useAuthProvider = (): AuthContextType => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    token: null,
    refreshToken: null,
    loading: true,
    error: null
  });

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = localStorage.getItem('auth_token');
        const refreshToken = localStorage.getItem('refresh_token');
        const userStr = localStorage.getItem('user_data');

        if (token && userStr) {
          const user = JSON.parse(userStr);
          setAuthState({
            isAuthenticated: true,
            user,
            token,
            refreshToken,
            loading: false,
            error: null
          });

          // Verify token validity
          await refreshAuth();
        } else {
          setAuthState(prev => ({ ...prev, loading: false }));
        }
      } catch (error) {
        console.error('Auth initialization failed:', error);
        logout();
      }
    };

    initializeAuth();
  }, []);

  const login = useCallback(async (credentials: LoginRequest): Promise<boolean> => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }));

      const response = await apiService.request('/api/auth/login', {
        method: 'POST',
        body: JSON.stringify(credentials)
      });

      if (response.success) {
        const { user, token, refreshToken } = response.data as LoginResponse;

        // Store auth data
        localStorage.setItem('auth_token', token);
        localStorage.setItem('refresh_token', refreshToken);
        localStorage.setItem('user_data', JSON.stringify(user));

        setAuthState({
          isAuthenticated: true,
          user,
          token,
          refreshToken,
          loading: false,
          error: null
        });

        return true;
      } else {
        setAuthState(prev => ({
          ...prev,
          loading: false,
          error: response.error || 'Login failed'
        }));
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }));
      return false;
    }
  }, []);

  const logout = useCallback(() => {
    // Clear storage
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user_data');

    // Reset state
    setAuthState({
      isAuthenticated: false,
      user: null,
      token: null,
      refreshToken: null,
      loading: false,
      error: null
    });

    // Optional: Call logout API
    apiService.request('/api/auth/logout', { method: 'POST' }).catch(console.error);
  }, []);

  const refreshAuth = useCallback(async (): Promise<boolean> => {
    try {
      const refreshToken = localStorage.getItem('refresh_token');
      if (!refreshToken) {
        logout();
        return false;
      }

      const response = await apiService.request('/api/auth/refresh', {
        method: 'POST',
        body: JSON.stringify({ refreshToken })
      });

      if (response.success) {
        const { user, token, refreshToken: newRefreshToken } = response.data;

        localStorage.setItem('auth_token', token);
        localStorage.setItem('refresh_token', newRefreshToken);
        localStorage.setItem('user_data', JSON.stringify(user));

        setAuthState(prev => ({
          ...prev,
          user,
          token,
          refreshToken: newRefreshToken,
          error: null
        }));

        return true;
      } else {
        logout();
        return false;
      }
    } catch (error) {
      console.error('Auth refresh failed:', error);
      logout();
      return false;
    }
  }, [logout]);

  const hasPermission = useCallback((permission: PermissionName, resource?: any): boolean => {
    if (!authState.user) return false;

    // Super admin has all permissions
    if (authState.user.roles.some(role => role.name === 'super_admin')) {
      return true;
    }

    // Check direct user permissions
    const userPermissions = authState.user.permissions || [];
    if (userPermissions.some(p => p.name === permission)) {
      return checkPermissionConditions(userPermissions.find(p => p.name === permission)!, resource);
    }

    // Check role permissions
    for (const role of authState.user.roles) {
      const rolePermissions = role.permissions || [];
      const rolePermission = rolePermissions.find(p => p.name === permission);
      if (rolePermission && checkPermissionConditions(rolePermission, resource)) {
        return true;
      }
    }

    return false;
  }, [authState.user]);

  const hasRole = useCallback((roleName: string): boolean => {
    if (!authState.user) return false;
    return authState.user.roles.some(role => role.name === roleName);
  }, [authState.user]);

  const hasAnyPermission = useCallback((permissions: PermissionName[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  }, [hasPermission]);

  const hasAllPermissions = useCallback((permissions: PermissionName[]): boolean => {
    return permissions.every(permission => hasPermission(permission));
  }, [hasPermission]);

  const updateUserPreferences = useCallback(async (preferences: Partial<User['preferences']>): Promise<boolean> => {
    if (!authState.user) return false;

    try {
      const response = await apiService.request('/api/auth/preferences', {
        method: 'PUT',
        body: JSON.stringify(preferences)
      });

      if (response.success) {
        const updatedUser = {
          ...authState.user,
          preferences: { ...authState.user.preferences, ...preferences }
        };

        localStorage.setItem('user_data', JSON.stringify(updatedUser));
        setAuthState(prev => ({ ...prev, user: updatedUser }));

        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to update preferences:', error);
      return false;
    }
  }, [authState.user]);

  return {
    ...authState,
    login,
    logout,
    refreshAuth,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    updateUserPreferences
  };
};

// Helper function to check permission conditions
function checkPermissionConditions(permission: Permission, resource?: any): boolean {
  if (!permission.conditions || permission.conditions.length === 0) {
    return true;
  }

  if (!resource) {
    return false;
  }

  return permission.conditions.every(condition => {
    const resourceValue = getNestedValue(resource, condition.field);
    
    switch (condition.operator) {
      case 'eq':
        return resourceValue === condition.value;
      case 'ne':
        return resourceValue !== condition.value;
      case 'in':
        return Array.isArray(condition.value) && condition.value.includes(resourceValue);
      case 'nin':
        return Array.isArray(condition.value) && !condition.value.includes(resourceValue);
      case 'gt':
        return resourceValue > condition.value;
      case 'gte':
        return resourceValue >= condition.value;
      case 'lt':
        return resourceValue < condition.value;
      case 'lte':
        return resourceValue <= condition.value;
      case 'contains':
        return String(resourceValue).includes(String(condition.value));
      case 'startsWith':
        return String(resourceValue).startsWith(String(condition.value));
      case 'endsWith':
        return String(resourceValue).endsWith(String(condition.value));
      default:
        return false;
    }
  });
}

// Helper function to get nested object values
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

export { AuthContext };
