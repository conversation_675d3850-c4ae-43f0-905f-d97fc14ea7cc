import React, { useState, useEffect } from 'react';
import { Users, Plus, Edit, Trash2, Shield, Search, Filter, MoreVertical } from 'lucide-react';
import { User, Role, PERMISSIONS } from '../types/auth';
import PermissionGuard, { PermissionButton } from './auth/PermissionGuard';
import LoadingSpinner from './common/LoadingSpinner';
import ErrorMessage from './common/ErrorMessage';

interface UserManagementProps {
  className?: string;
}

const UserManagement: React.FC<UserManagementProps> = ({ className = '' }) => {
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);

  useEffect(() => {
    loadUsers();
    loadRoles();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);
      // Mock data - replace with actual API call
      const mockUsers: User[] = [
        {
          id: '1',
          username: 'admin',
          email: '<EMAIL>',
          fullName: '系统管理员',
          roles: [
            {
              id: 'role_1',
              name: 'admin',
              displayName: '管理员',
              description: '系统管理员角色',
              permissions: [],
              isSystem: true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          ],
          permissions: [],
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          preferences: {
            theme: 'dark',
            language: 'zh-CN',
            timezone: 'Asia/Shanghai',
            notifications: { email: true, push: true, sms: false },
            dashboard: { layout: 'grid', widgets: [] }
          },
          department: 'IT部门',
          position: '系统管理员'
        },
        {
          id: '2',
          username: 'analyst1',
          email: '<EMAIL>',
          fullName: '张分析师',
          roles: [
            {
              id: 'role_2',
              name: 'analyst',
              displayName: '分析师',
              description: '数据分析师角色',
              permissions: [],
              isSystem: true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          ],
          permissions: [],
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          preferences: {
            theme: 'light',
            language: 'zh-CN',
            timezone: 'Asia/Shanghai',
            notifications: { email: true, push: false, sms: false },
            dashboard: { layout: 'list', widgets: [] }
          },
          department: '业务部门',
          position: '高级分析师'
        }
      ];
      setUsers(mockUsers);
    } catch (err) {
      setError('加载用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  const loadRoles = async () => {
    try {
      // Mock data - replace with actual API call
      const mockRoles: Role[] = [
        {
          id: 'role_1',
          name: 'admin',
          displayName: '管理员',
          description: '系统管理员，拥有所有权限',
          permissions: [],
          isSystem: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'role_2',
          name: 'analyst',
          displayName: '分析师',
          description: '数据分析师，可以进行企业分析和报告生成',
          permissions: [],
          isSystem: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'role_3',
          name: 'viewer',
          displayName: '查看者',
          description: '只能查看数据，无法编辑',
          permissions: [],
          isSystem: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];
      setRoles(mockRoles);
    } catch (err) {
      console.error('加载角色列表失败:', err);
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRole = !selectedRole || user.roles.some(role => role.name === selectedRole);
    
    return matchesSearch && matchesRole;
  });

  const handleDeleteUser = async (userId: string) => {
    if (window.confirm('确定要删除这个用户吗？')) {
      try {
        // Mock delete - replace with actual API call
        setUsers(prev => prev.filter(user => user.id !== userId));
      } catch (err) {
        setError('删除用户失败');
      }
    }
  };

  const handleToggleUserStatus = async (userId: string) => {
    try {
      // Mock toggle - replace with actual API call
      setUsers(prev => prev.map(user => 
        user.id === userId ? { ...user, isActive: !user.isActive } : user
      ));
    } catch (err) {
      setError('更新用户状态失败');
    }
  };

  if (loading) {
    return (
      <div className={`bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50 ${className}`}>
        <LoadingSpinner size="md" text="加载用户数据中..." />
      </div>
    );
  }

  return (
    <PermissionGuard permission={PERMISSIONS.USER_VIEW}>
      <div className={`bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 ${className}`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-slate-700/50">
          <div className="flex items-center space-x-3">
            <Users className="w-6 h-6 text-blue-400" />
            <div>
              <h2 className="text-xl font-semibold text-white">用户管理</h2>
              <p className="text-slate-400 text-sm mt-1">管理系统用户和权限</p>
            </div>
          </div>
          
          <PermissionButton
            permission={PERMISSIONS.USER_CREATE}
            onClick={() => setShowCreateModal(true)}
            variant="primary"
            className="flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>添加用户</span>
          </PermissionButton>
        </div>

        {error && (
          <div className="p-6 border-b border-slate-700/50">
            <ErrorMessage message={error} onRetry={() => setError(null)} />
          </div>
        )}

        {/* Filters */}
        <div className="p-6 border-b border-slate-700/50">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                <input
                  type="text"
                  placeholder="搜索用户..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            <div className="sm:w-48">
              <select
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value)}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">所有角色</option>
                {roles.map(role => (
                  <option key={role.id} value={role.name}>
                    {role.displayName}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Users Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-slate-700/50">
                <th className="text-left p-4 text-slate-300 font-medium">用户</th>
                <th className="text-left p-4 text-slate-300 font-medium">角色</th>
                <th className="text-left p-4 text-slate-300 font-medium">部门</th>
                <th className="text-left p-4 text-slate-300 font-medium">状态</th>
                <th className="text-left p-4 text-slate-300 font-medium">最后登录</th>
                <th className="text-right p-4 text-slate-300 font-medium">操作</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.map((user) => (
                <tr key={user.id} className="border-b border-slate-700/50 hover:bg-slate-700/30">
                  <td className="p-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                        <span className="text-blue-400 font-medium">
                          {user.fullName.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <div className="text-white font-medium">{user.fullName}</div>
                        <div className="text-slate-400 text-sm">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="p-4">
                    <div className="flex flex-wrap gap-1">
                      {user.roles.map(role => (
                        <span
                          key={role.id}
                          className="px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full"
                        >
                          {role.displayName}
                        </span>
                      ))}
                    </div>
                  </td>
                  <td className="p-4">
                    <div className="text-slate-300">{user.department || '-'}</div>
                    <div className="text-slate-400 text-sm">{user.position || '-'}</div>
                  </td>
                  <td className="p-4">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      user.isActive 
                        ? 'bg-green-500/20 text-green-400' 
                        : 'bg-red-500/20 text-red-400'
                    }`}>
                      {user.isActive ? '活跃' : '禁用'}
                    </span>
                  </td>
                  <td className="p-4 text-slate-300">
                    {user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString() : '从未登录'}
                  </td>
                  <td className="p-4">
                    <div className="flex items-center justify-end space-x-2">
                      <PermissionButton
                        permission={PERMISSIONS.USER_EDIT}
                        onClick={() => setEditingUser(user)}
                        variant="secondary"
                        className="p-2"
                      >
                        <Edit className="w-4 h-4" />
                      </PermissionButton>
                      
                      <PermissionButton
                        permission={PERMISSIONS.USER_DELETE}
                        onClick={() => handleDeleteUser(user.id)}
                        variant="danger"
                        className="p-2"
                      >
                        <Trash2 className="w-4 h-4" />
                      </PermissionButton>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredUsers.length === 0 && (
          <div className="p-8 text-center">
            <Users className="w-12 h-12 text-slate-600 mx-auto mb-4" />
            <p className="text-slate-400">没有找到匹配的用户</p>
          </div>
        )}
      </div>
    </PermissionGuard>
  );
};

export default UserManagement;
