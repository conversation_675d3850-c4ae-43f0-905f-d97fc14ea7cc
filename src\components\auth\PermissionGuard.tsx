import React from 'react';
import { useAuth } from '../../hooks/useAuth';
import { PermissionName } from '../../types/auth';
import { AlertTriangle, Lock } from 'lucide-react';

interface PermissionGuardProps {
  permission?: PermissionName;
  permissions?: PermissionName[];
  role?: string;
  roles?: string[];
  requireAll?: boolean; // For multiple permissions: require all (AND) or any (OR)
  resource?: any; // Resource object for condition-based permissions
  fallback?: React.ReactNode;
  children: React.ReactNode;
  showFallback?: boolean; // Whether to show fallback UI or hide completely
}

const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permission,
  permissions,
  role,
  roles,
  requireAll = false,
  resource,
  fallback,
  children,
  showFallback = true
}) => {
  const { hasPermission, hasRole, hasAnyPermission, hasAllPermissions } = useAuth();

  // Check single permission
  if (permission) {
    if (!hasPermission(permission, resource)) {
      return showFallback ? (fallback || <DefaultFallback />) : null;
    }
  }

  // Check multiple permissions
  if (permissions && permissions.length > 0) {
    const hasRequiredPermissions = requireAll 
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions);
    
    if (!hasRequiredPermissions) {
      return showFallback ? (fallback || <DefaultFallback />) : null;
    }
  }

  // Check single role
  if (role) {
    if (!hasRole(role)) {
      return showFallback ? (fallback || <DefaultFallback />) : null;
    }
  }

  // Check multiple roles
  if (roles && roles.length > 0) {
    const hasRequiredRole = roles.some(r => hasRole(r));
    if (!hasRequiredRole) {
      return showFallback ? (fallback || <DefaultFallback />) : null;
    }
  }

  return <>{children}</>;
};

const DefaultFallback: React.FC = () => (
  <div className="flex items-center justify-center p-8 bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50">
    <div className="text-center">
      <Lock className="w-12 h-12 text-slate-400 mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-white mb-2">访问受限</h3>
      <p className="text-slate-400">您没有权限访问此功能</p>
    </div>
  </div>
);

export default PermissionGuard;

// Hook for conditional rendering based on permissions
export const usePermissionCheck = () => {
  const { hasPermission, hasRole, hasAnyPermission, hasAllPermissions } = useAuth();

  const canAccess = React.useCallback((options: {
    permission?: PermissionName;
    permissions?: PermissionName[];
    role?: string;
    roles?: string[];
    requireAll?: boolean;
    resource?: any;
  }) => {
    const { permission, permissions, role, roles, requireAll = false, resource } = options;

    // Check single permission
    if (permission && !hasPermission(permission, resource)) {
      return false;
    }

    // Check multiple permissions
    if (permissions && permissions.length > 0) {
      const hasRequiredPermissions = requireAll 
        ? hasAllPermissions(permissions)
        : hasAnyPermission(permissions);
      
      if (!hasRequiredPermissions) {
        return false;
      }
    }

    // Check single role
    if (role && !hasRole(role)) {
      return false;
    }

    // Check multiple roles
    if (roles && roles.length > 0) {
      const hasRequiredRole = roles.some(r => hasRole(r));
      if (!hasRequiredRole) {
        return false;
      }
    }

    return true;
  }, [hasPermission, hasRole, hasAnyPermission, hasAllPermissions]);

  return { canAccess };
};

// Higher-order component for permission-based access control
export const withPermission = <P extends object>(
  Component: React.ComponentType<P>,
  permissionOptions: {
    permission?: PermissionName;
    permissions?: PermissionName[];
    role?: string;
    roles?: string[];
    requireAll?: boolean;
    fallback?: React.ComponentType;
  }
) => {
  const WrappedComponent: React.FC<P> = (props) => {
    const FallbackComponent = permissionOptions.fallback || DefaultFallback;

    return (
      <PermissionGuard
        {...permissionOptions}
        fallback={<FallbackComponent />}
      >
        <Component {...props} />
      </PermissionGuard>
    );
  };

  WrappedComponent.displayName = `withPermission(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

// Component for displaying permission-based buttons
interface PermissionButtonProps {
  permission?: PermissionName;
  permissions?: PermissionName[];
  role?: string;
  roles?: string[];
  requireAll?: boolean;
  resource?: any;
  onClick: () => void;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'danger' | 'success';
}

export const PermissionButton: React.FC<PermissionButtonProps> = ({
  permission,
  permissions,
  role,
  roles,
  requireAll = false,
  resource,
  onClick,
  children,
  className = '',
  disabled = false,
  variant = 'primary'
}) => {
  const { canAccess } = usePermissionCheck();

  const hasAccess = canAccess({
    permission,
    permissions,
    role,
    roles,
    requireAll,
    resource
  });

  if (!hasAccess) {
    return null;
  }

  const variantClasses = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white',
    secondary: 'bg-slate-600 hover:bg-slate-700 text-white',
    danger: 'bg-red-600 hover:bg-red-700 text-white',
    success: 'bg-green-600 hover:bg-green-700 text-white'
  };

  return (
    <button
      type="button"
      onClick={onClick}
      disabled={disabled}
      className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${variantClasses[variant]} ${className}`}
    >
      {children}
    </button>
  );
};

// Component for displaying permission-based menu items
interface PermissionMenuItemProps {
  permission?: PermissionName;
  permissions?: PermissionName[];
  role?: string;
  roles?: string[];
  requireAll?: boolean;
  resource?: any;
  onClick: () => void;
  children: React.ReactNode;
  className?: string;
  icon?: React.ReactNode;
}

export const PermissionMenuItem: React.FC<PermissionMenuItemProps> = ({
  permission,
  permissions,
  role,
  roles,
  requireAll = false,
  resource,
  onClick,
  children,
  className = '',
  icon
}) => {
  const { canAccess } = usePermissionCheck();

  const hasAccess = canAccess({
    permission,
    permissions,
    role,
    roles,
    requireAll,
    resource
  });

  if (!hasAccess) {
    return null;
  }

  return (
    <button
      type="button"
      onClick={onClick}
      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors duration-200 hover:bg-slate-700/50 text-slate-300 hover:text-white ${className}`}
    >
      {icon && <span className="flex-shrink-0">{icon}</span>}
      <span>{children}</span>
    </button>
  );
};
