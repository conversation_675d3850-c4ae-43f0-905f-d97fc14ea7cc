import pkg from 'pg';
const { Pool } = pkg;
import { getPostgreSQLPool } from '../config/database.js';
import { dbLogger } from '../utils/logger.js';
import { CustomError } from '../middleware/errorHandler.js';

export interface AnalyticsRecord {
  id?: number;
  enterprise_id: string;
  analysis_type: string;
  result_data: any;
  confidence_score?: number;
  created_at?: Date;
  updated_at?: Date;
}

export interface UserActivity {
  id?: number;
  user_id?: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  metadata?: any;
  ip_address?: string;
  user_agent?: string;
  created_at?: Date;
}

export interface ReportData {
  id?: number;
  name: string;
  description?: string;
  report_type: string;
  data: any;
  generated_by?: string;
  created_at?: Date;
  updated_at?: Date;
}

export class PostgresService {
  private pool: pkg.Pool | null = null;

  constructor() {
    // Pool will be initialized lazily when needed
  }

  private getPool(): pkg.Pool {
    if (!this.pool) {
      this.pool = getPostgreSQLPool();
    }
    return this.pool;
  }

  /**
   * 初始化数据库表
   */
  async initializeTables(): Promise<void> {
    const client = await this.getPool().connect();
    
    try {
      // 创建分析结果表
      await client.query(`
        CREATE TABLE IF NOT EXISTS analytics_results (
          id SERIAL PRIMARY KEY,
          enterprise_id VARCHAR(24) NOT NULL,
          analysis_type VARCHAR(50) NOT NULL,
          result_data JSONB NOT NULL,
          confidence_score DECIMAL(5,2),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // 创建用户活动表
      await client.query(`
        CREATE TABLE IF NOT EXISTS user_activities (
          id SERIAL PRIMARY KEY,
          user_id VARCHAR(50),
          action VARCHAR(100) NOT NULL,
          resource_type VARCHAR(50) NOT NULL,
          resource_id VARCHAR(50),
          metadata JSONB,
          ip_address INET,
          user_agent TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // 创建报告表
      await client.query(`
        CREATE TABLE IF NOT EXISTS reports (
          id SERIAL PRIMARY KEY,
          name VARCHAR(200) NOT NULL,
          description TEXT,
          report_type VARCHAR(50) NOT NULL,
          data JSONB NOT NULL,
          generated_by VARCHAR(50),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // 创建索引
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_analytics_enterprise_id 
        ON analytics_results(enterprise_id)
      `);
      
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_analytics_type 
        ON analytics_results(analysis_type)
      `);
      
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_analytics_created_at 
        ON analytics_results(created_at DESC)
      `);

      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_user_activities_user_id 
        ON user_activities(user_id)
      `);
      
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_user_activities_created_at 
        ON user_activities(created_at DESC)
      `);

      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_reports_type 
        ON reports(report_type)
      `);

      dbLogger.info('PostgreSQL tables initialized successfully');
    } catch (error) {
      dbLogger.error('Failed to initialize PostgreSQL tables:', error);
      throw new CustomError('数据库表初始化失败', 500);
    } finally {
      client.release();
    }
  }

  /**
   * 保存分析结果
   */
  async saveAnalyticsResult(record: AnalyticsRecord): Promise<AnalyticsRecord> {
    const client = await this.getPool().connect();
    
    try {
      const query = `
        INSERT INTO analytics_results (enterprise_id, analysis_type, result_data, confidence_score)
        VALUES ($1, $2, $3, $4)
        RETURNING *
      `;
      
      const values = [
        record.enterprise_id,
        record.analysis_type,
        JSON.stringify(record.result_data),
        record.confidence_score
      ];

      const result = await client.query(query, values);
      const savedRecord = result.rows[0];

      dbLogger.info('Saved analytics result', { 
        id: savedRecord.id, 
        enterprise_id: record.enterprise_id,
        analysis_type: record.analysis_type
      });

      return {
        ...savedRecord,
        result_data: JSON.parse(savedRecord.result_data)
      };
    } catch (error) {
      dbLogger.error('Failed to save analytics result:', error);
      throw new CustomError('保存分析结果失败', 500);
    } finally {
      client.release();
    }
  }

  /**
   * 获取企业的分析结果
   */
  async getAnalyticsResults(
    enterpriseId: string,
    analysisType?: string,
    limit: number = 10
  ): Promise<AnalyticsRecord[]> {
    const client = await this.getPool().connect();
    
    try {
      let query = `
        SELECT * FROM analytics_results 
        WHERE enterprise_id = $1
      `;
      const values: any[] = [enterpriseId];

      if (analysisType) {
        query += ' AND analysis_type = $2';
        values.push(analysisType);
      }

      query += ' ORDER BY created_at DESC LIMIT $' + (values.length + 1);
      values.push(limit);

      const result = await client.query(query, values);
      
      return result.rows.map(row => ({
        ...row,
        result_data: JSON.parse(row.result_data)
      }));
    } catch (error) {
      dbLogger.error('Failed to get analytics results:', error);
      throw new CustomError('获取分析结果失败', 500);
    } finally {
      client.release();
    }
  }

  /**
   * 记录用户活动
   */
  async logUserActivity(activity: UserActivity): Promise<UserActivity> {
    const client = await this.getPool().connect();
    
    try {
      const query = `
        INSERT INTO user_activities (user_id, action, resource_type, resource_id, metadata, ip_address, user_agent)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING *
      `;
      
      const values = [
        activity.user_id,
        activity.action,
        activity.resource_type,
        activity.resource_id,
        activity.metadata ? JSON.stringify(activity.metadata) : null,
        activity.ip_address,
        activity.user_agent
      ];

      const result = await client.query(query, values);
      const savedActivity = result.rows[0];

      return {
        ...savedActivity,
        metadata: savedActivity.metadata ? JSON.parse(savedActivity.metadata) : null
      };
    } catch (error) {
      dbLogger.error('Failed to log user activity:', error);
      throw new CustomError('记录用户活动失败', 500);
    } finally {
      client.release();
    }
  }

  /**
   * 获取用户活动记录
   */
  async getUserActivities(
    userId?: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<{ activities: UserActivity[]; total: number }> {
    const client = await this.getPool().connect();
    
    try {
      let whereClause = '';
      const values: any[] = [];

      if (userId) {
        whereClause = 'WHERE user_id = $1';
        values.push(userId);
      }

      // Get total count
      const countQuery = `SELECT COUNT(*) FROM user_activities ${whereClause}`;
      const countResult = await client.query(countQuery, values);
      const total = parseInt(countResult.rows[0].count);

      // Get activities
      const query = `
        SELECT * FROM user_activities 
        ${whereClause}
        ORDER BY created_at DESC 
        LIMIT $${values.length + 1} OFFSET $${values.length + 2}
      `;
      values.push(limit, offset);

      const result = await client.query(query, values);
      
      const activities = result.rows.map(row => ({
        ...row,
        metadata: row.metadata ? JSON.parse(row.metadata) : null
      }));

      return { activities, total };
    } catch (error) {
      dbLogger.error('Failed to get user activities:', error);
      throw new CustomError('获取用户活动失败', 500);
    } finally {
      client.release();
    }
  }

  /**
   * 保存报告
   */
  async saveReport(report: ReportData): Promise<ReportData> {
    const client = await this.getPool().connect();
    
    try {
      const query = `
        INSERT INTO reports (name, description, report_type, data, generated_by)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `;
      
      const values = [
        report.name,
        report.description,
        report.report_type,
        JSON.stringify(report.data),
        report.generated_by
      ];

      const result = await client.query(query, values);
      const savedReport = result.rows[0];

      dbLogger.info('Saved report', { 
        id: savedReport.id, 
        name: report.name,
        type: report.report_type
      });

      return {
        ...savedReport,
        data: JSON.parse(savedReport.data)
      };
    } catch (error) {
      dbLogger.error('Failed to save report:', error);
      throw new CustomError('保存报告失败', 500);
    } finally {
      client.release();
    }
  }

  /**
   * 获取报告列表
   */
  async getReports(
    reportType?: string,
    limit: number = 20,
    offset: number = 0
  ): Promise<{ reports: ReportData[]; total: number }> {
    const client = await this.getPool().connect();
    
    try {
      let whereClause = '';
      const values: any[] = [];

      if (reportType) {
        whereClause = 'WHERE report_type = $1';
        values.push(reportType);
      }

      // Get total count
      const countQuery = `SELECT COUNT(*) FROM reports ${whereClause}`;
      const countResult = await client.query(countQuery, values);
      const total = parseInt(countResult.rows[0].count);

      // Get reports
      const query = `
        SELECT * FROM reports 
        ${whereClause}
        ORDER BY created_at DESC 
        LIMIT $${values.length + 1} OFFSET $${values.length + 2}
      `;
      values.push(limit, offset);

      const result = await client.query(query, values);
      
      const reports = result.rows.map(row => ({
        ...row,
        data: JSON.parse(row.data)
      }));

      return { reports, total };
    } catch (error) {
      dbLogger.error('Failed to get reports:', error);
      throw new CustomError('获取报告失败', 500);
    } finally {
      client.release();
    }
  }

  /**
   * 获取分析统计信息
   */
  async getAnalyticsStats(): Promise<{
    totalAnalyses: number;
    analysesByType: Record<string, number>;
    recentAnalyses: number;
    averageConfidence: number;
  }> {
    const client = await this.getPool().connect();
    
    try {
      const [totalResult, typeResult, recentResult, confidenceResult] = await Promise.all([
        client.query('SELECT COUNT(*) FROM analytics_results'),
        client.query(`
          SELECT analysis_type, COUNT(*) as count 
          FROM analytics_results 
          GROUP BY analysis_type
        `),
        client.query(`
          SELECT COUNT(*) FROM analytics_results 
          WHERE created_at >= NOW() - INTERVAL '7 days'
        `),
        client.query(`
          SELECT AVG(confidence_score) as avg_confidence 
          FROM analytics_results 
          WHERE confidence_score IS NOT NULL
        `)
      ]);

      const analysesByType: Record<string, number> = {};
      typeResult.rows.forEach(row => {
        analysesByType[row.analysis_type] = parseInt(row.count);
      });

      return {
        totalAnalyses: parseInt(totalResult.rows[0].count),
        analysesByType,
        recentAnalyses: parseInt(recentResult.rows[0].count),
        averageConfidence: parseFloat(confidenceResult.rows[0].avg_confidence || '0')
      };
    } catch (error) {
      dbLogger.error('Failed to get analytics stats:', error);
      throw new CustomError('获取分析统计失败', 500);
    } finally {
      client.release();
    }
  }
}

// Export a function to create the service instance when needed
export const createPostgresService = () => new PostgresService();
