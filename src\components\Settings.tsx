import React, { useState, useEffect } from 'react';
import { Settings as SettingsIcon, Database, Brain, Shield, Bell, User, Globe, Save, RefreshCw, Download, Upload, CheckCircle } from 'lucide-react';
import { apiService } from '../services/api';
import { useApiCall } from '../hooks/useApiCall';
import LoadingSpinner from './common/LoadingSpinner';
import ErrorMessage from './common/ErrorMessage';

const Settings = () => {
  const [activeSection, setActiveSection] = useState('database');
  const [settings, setSettings] = useState(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [testingConnection, setTestingConnection] = useState('');
  const [systemInfo, setSystemInfo] = useState(null);

  // Mock settings data since API is not available
  const settingsLoading = false;
  const settingsError = null;
  const saveLoading = false;
  const saveError = null;

  useEffect(() => {
    // Initialize with mock data
    setSettings({
      database: {
        mongodb: { host: 'mongodb://localhost:27017', status: 'connected' },
        neo4j: { host: 'bolt://localhost:7687', status: 'connected' },
        postgresql: { host: 'postgresql://localhost:5432', status: 'connected' }
      },
      ai: {
        matchingThreshold: 85,
        recommendationLimit: 20,
        learningRate: 0.003
      }
    });
    loadSystemInfo();
  }, []);

  const loadSystemInfo = async () => {
    // Mock system info
    setSystemInfo({
      version: { system: 'v2.1.3', aiEngine: 'v1.8.2', database: 'MongoDB 6.0' },
      runtime: { uptime: '15天 8小时', memory: { used: '2.3GB', total: '8GB', percentage: 29 } },
      services: { mongodb: 'running', neo4j: 'running', postgresql: 'running', aiEngine: 'running' }
    });
  };

  const handleSettingsChange = (section: string, key: string, value: any) => {
    if (!settings) return;

    const newSettings = {
      ...settings,
      [section]: {
        ...settings[section],
        [key]: value
      }
    };
    setSettings(newSettings);
    setHasChanges(true);
  };

  const handleSaveSettings = async () => {
    if (!settings) return;

    const result = await saveSettings(settings);
    if (result) {
      setHasChanges(false);
      alert('设置保存成功');
    }
  };

  const testConnection = async (dbType: string) => {
    setTestingConnection(dbType);
    try {
      const response = await apiService.testDatabaseConnection(dbType);
      if (response.success) {
        alert(`${dbType} 连接测试成功: ${response.data.status}`);
      } else {
        alert(`${dbType} 连接测试失败: ${response.error}`);
      }
    } catch (error) {
      alert(`${dbType} 连接测试失败`);
    } finally {
      setTestingConnection('');
    }
  };

  const handleBackup = async () => {
    try {
      const response = await apiService.backupDatabase();
      if (response.success) {
        alert('数据库备份成功');
      } else {
        alert('数据库备份失败: ' + response.error);
      }
    } catch (error) {
      alert('数据库备份失败');
    }
  };

  const settingSections = [
    { id: 'database', name: '数据库配置', icon: Database },
    { id: 'ai', name: 'AI引擎设置', icon: Brain },
    { id: 'security', name: '安全设置', icon: Shield },
    { id: 'notifications', name: '通知设置', icon: Bell },
    { id: 'profile', name: '个人资料', icon: User },
    { id: 'system', name: '系统设置', icon: Globe },
  ];

  const databaseConfigs = settings?.database ? [
    {
      name: 'MongoDB',
      status: '已连接',
      host: settings.database.mongodb.host,
      database: settings.database.mongodb.database,
      type: 'mongodb'
    },
    {
      name: 'Neo4j',
      status: '已连接',
      host: settings.database.neo4j.host,
      database: settings.database.neo4j.database,
      type: 'neo4j'
    },
    {
      name: 'PostgreSQL',
      status: '已连接',
      host: settings.database.postgresql.host,
      database: settings.database.postgresql.database,
      type: 'postgresql'
    },
  ] : [];

  const aiSettings = settings?.ai?.models ? [
    { name: '画像分析模型', value: settings.ai.models.profile, status: '运行中' },
    { name: '数据处理引擎', value: settings.ai.models.dataProcessing, status: '运行中' },
    { name: '推荐算法', value: settings.ai.models.recommendation, status: '运行中' },
    { name: '自然语言处理', value: settings.ai.models.nlp, status: '运行中' },
  ] : [];

  const renderDatabaseSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">数据库连接状态</h3>
        <div className="space-y-4">
          {databaseConfigs.map((db, index) => (
            <div key={index} className="bg-slate-700/50 rounded-lg p-4 border border-slate-600/50">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <Database className="w-5 h-5 text-blue-400" />
                  <span className="text-white font-medium">{db.name}</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    db.status === '已连接' ? 'bg-green-500/20 text-green-300' : 'bg-red-500/20 text-red-300'
                  }`}>
                    {db.status}
                  </span>
                </div>
                <button
                  type="button"
                  onClick={() => testConnection(db.type)}
                  disabled={testingConnection === db.type}
                  className="px-3 py-1 bg-blue-600 hover:bg-blue-700 disabled:bg-slate-600 disabled:cursor-not-allowed text-white text-sm rounded transition-colors duration-200 flex items-center space-x-1"
                >
                  {testingConnection === db.type ? (
                    <>
                      <RefreshCw className="w-3 h-3 animate-spin" />
                      <span>测试中</span>
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-3 h-3" />
                      <span>测试连接</span>
                    </>
                  )}
                </button>
              </div>
              <div className="text-sm text-slate-400">
                <p>主机: {db.host}</p>
                <p>数据库: {db.database}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-white mb-4">数据同步设置</h3>
        <div className="bg-slate-700/50 rounded-lg p-4 border border-slate-600/50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">同步频率</label>
              <select className="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white text-sm">
                <option>实时同步</option>
                <option>每小时</option>
                <option>每日</option>
                <option>每周</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">备份策略</label>
              <select className="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white text-sm">
                <option>自动备份</option>
                <option>手动备份</option>
                <option>定时备份</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderAISettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">AI引擎状态</h3>
        <div className="space-y-4">
          {aiSettings.map((setting, index) => (
            <div key={index} className="bg-slate-700/50 rounded-lg p-4 border border-slate-600/50">
              <div className="flex items-center justify-between">
                <div>
                  <span className="text-white font-medium">{setting.name}</span>
                  <p className="text-slate-400 text-sm">{setting.value}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="px-2 py-1 bg-green-500/20 text-green-300 rounded-full text-xs font-medium">
                    {setting.status}
                  </span>
                  <button className="px-3 py-1 bg-slate-600 hover:bg-slate-500 text-white text-sm rounded transition-colors duration-200">
                    配置
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-white mb-4">模型参数调优</h3>
        <div className="bg-slate-700/50 rounded-lg p-4 border border-slate-600/50">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">匹配精度阈值</label>
              <div className="flex items-center space-x-4">
                <input type="range" min="0" max="100" value="85" className="flex-1" />
                <span className="text-white text-sm w-12">85%</span>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">推荐数量限制</label>
              <div className="flex items-center space-x-4">
                <input type="range" min="5" max="50" value="20" className="flex-1" />
                <span className="text-white text-sm w-12">20</span>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">学习率</label>
              <div className="flex items-center space-x-4">
                <input type="range" min="1" max="10" value="3" className="flex-1" />
                <span className="text-white text-sm w-12">0.003</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">访问控制</h3>
        <div className="space-y-4">
          <div className="bg-slate-700/50 rounded-lg p-4 border border-slate-600/50">
            <div className="flex items-center justify-between mb-3">
              <span className="text-white font-medium">双因素认证</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" className="sr-only peer" defaultChecked />
                <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            <p className="text-slate-400 text-sm">启用双因素认证以增强账户安全性</p>
          </div>

          <div className="bg-slate-700/50 rounded-lg p-4 border border-slate-600/50">
            <div className="flex items-center justify-between mb-3">
              <span className="text-white font-medium">数据加密</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" className="sr-only peer" defaultChecked />
                <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            <p className="text-slate-400 text-sm">对敏感数据进行AES-256加密</p>
          </div>

          <div className="bg-slate-700/50 rounded-lg p-4 border border-slate-600/50">
            <div className="flex items-center justify-between mb-3">
              <span className="text-white font-medium">API访问限制</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" className="sr-only peer" defaultChecked />
                <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            <p className="text-slate-400 text-sm">限制API调用频率和访问权限</p>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-white mb-4">密码策略</h3>
        <div className="bg-slate-700/50 rounded-lg p-4 border border-slate-600/50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">最小密码长度</label>
              <input type="number" value="8" className="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white text-sm" />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">密码过期天数</label>
              <input type="number" value="90" className="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white text-sm" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">通知偏好</h3>
        <div className="space-y-4">
          {[
            { name: '新企业画像完成', desc: '当AI完成新的企业画像分析时通知' },
            { name: '数据同步异常', desc: '当数据库同步出现问题时通知' },
            { name: '系统维护提醒', desc: '系统维护前24小时提醒' },
            { name: '匹配度更新', desc: '当企业匹配度发生重大变化时通知' },
            { name: '每日报告', desc: '每日分析报告推送' },
            { name: '每周总结', desc: '每周数据分析总结' },
          ].map((notification, index) => (
            <div key={index} className="bg-slate-700/50 rounded-lg p-4 border border-slate-600/50">
              <div className="flex items-center justify-between mb-2">
                <span className="text-white font-medium">{notification.name}</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" defaultChecked={index < 3} />
                  <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
              <p className="text-slate-400 text-sm">{notification.desc}</p>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-white mb-4">通知方式</h3>
        <div className="bg-slate-700/50 rounded-lg p-4 border border-slate-600/50">
          <div className="space-y-3">
            {['邮件通知', '短信通知', '系统内通知', '微信通知'].map((method, index) => (
              <label key={index} className="flex items-center space-x-3">
                <input type="checkbox" className="w-4 h-4 text-blue-600 bg-slate-600 border-slate-500 rounded" defaultChecked={index !== 1} />
                <span className="text-slate-300">{method}</span>
              </label>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderProfileSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">个人信息</h3>
        <div className="bg-slate-700/50 rounded-lg p-4 border border-slate-600/50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">姓名</label>
              <input type="text" value="张三" className="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white" />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">邮箱</label>
              <input type="email" value="<EMAIL>" className="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white" />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">部门</label>
              <input type="text" value="销售部" className="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white" />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">职位</label>
              <input type="text" value="销售经理" className="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white" />
            </div>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-white mb-4">偏好设置</h3>
        <div className="bg-slate-700/50 rounded-lg p-4 border border-slate-600/50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">界面语言</label>
              <select className="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white">
                <option>简体中文</option>
                <option>繁体中文</option>
                <option>English</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">时区</label>
              <select className="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white">
                <option>北京时间 (UTC+8)</option>
                <option>上海时间 (UTC+8)</option>
                <option>香港时间 (UTC+8)</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSystemSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">系统维护</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            type="button"
            onClick={() => alert('AI引擎重启功能开发中...')}
            className="flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200"
          >
            <RefreshCw className="w-4 h-4" />
            <span>重启AI引擎</span>
          </button>
          <button
            type="button"
            onClick={() => alert('数据库同步功能开发中...')}
            className="flex items-center justify-center space-x-2 px-4 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200"
          >
            <Database className="w-4 h-4" />
            <span>同步数据库</span>
          </button>
          <button
            type="button"
            onClick={() => alert('配置导出功能开发中...')}
            className="flex items-center justify-center space-x-2 px-4 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors duration-200"
          >
            <Download className="w-4 h-4" />
            <span>导出配置</span>
          </button>
          <button
            type="button"
            onClick={handleBackup}
            className="flex items-center justify-center space-x-2 px-4 py-3 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition-colors duration-200"
          >
            <Upload className="w-4 h-4" />
            <span>数据备份</span>
          </button>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-white mb-4">系统信息</h3>
        <div className="bg-slate-700/50 rounded-lg p-4 border border-slate-600/50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-slate-400">系统版本</span>
                <span className="text-white">v2.1.3</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">AI引擎版本</span>
                <span className="text-white">v1.8.2</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">数据库版本</span>
                <span className="text-white">MongoDB 6.0</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-slate-400">运行时间</span>
                <span className="text-white">15天 8小时</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">内存使用</span>
                <span className="text-white">2.3GB / 8GB</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">磁盘使用</span>
                <span className="text-white">45GB / 500GB</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'database':
        return renderDatabaseSettings();
      case 'ai':
        return renderAISettings();
      case 'security':
        return renderSecuritySettings();
      case 'notifications':
        return renderNotificationSettings();
      case 'profile':
        return renderProfileSettings();
      case 'system':
        return renderSystemSettings();
      default:
        return renderDatabaseSettings();
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">系统设置</h1>
          <p className="text-slate-400">配置和管理企业画像师平台的各项设置</p>
        </div>
        <div className="flex items-center space-x-3">
          {hasChanges && (
            <span className="text-yellow-400 text-sm">有未保存的更改</span>
          )}
          <button
            type="button"
            onClick={handleSaveSettings}
            disabled={!hasChanges || saveLoading}
            className="px-6 py-3 bg-green-600 hover:bg-green-700 disabled:bg-slate-600 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"
          >
            {saveLoading ? (
              <>
                <RefreshCw className="w-4 h-4 animate-spin" />
                <span>保存中...</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span>保存设置</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Loading and Error States */}
      {settingsLoading && (
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-12 border border-slate-700/50">
          <LoadingSpinner size="lg" text="加载设置中..." />
        </div>
      )}

      {settingsError && (
        <ErrorMessage
          message={settingsError}
          onRetry={loadSettings}
          className="mb-6"
        />
      )}

      {saveError && (
        <ErrorMessage
          message={saveError}
          className="mb-6"
        />
      )}

      {!settingsLoading && settings && (

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Settings Navigation */}
        <div className="lg:col-span-1">
          <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-4 border border-slate-700/50">
            <h2 className="text-lg font-semibold text-white mb-4">设置分类</h2>
            <div className="space-y-2">
              {settingSections.map((section) => {
                const Icon = section.icon;
                return (
                  <button
                    type="button"
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 ${
                      activeSection === section.id
                        ? 'bg-blue-600 text-white'
                        : 'text-slate-400 hover:text-white hover:bg-slate-700/50'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="text-sm font-medium">{section.name}</span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
            {renderContent()}
          </div>
        </div>
      </div>
      )}
    </div>
  );
};

export default Settings;