import { useEffect, useRef, useState, useCallback } from 'react';
// import { io, Socket } from 'socket.io-client';

export interface Notification {
  type: 'profile_update' | 'analysis_complete' | 'system_alert' | 'user_message';
  title: string;
  message: string;
  data?: any;
  timestamp: string;
  userId?: string;
  profileId?: string;
}

export interface UseWebSocketReturn {
  isConnected: boolean;
  notifications: Notification[];
  sendMessage: (event: string, data: any) => void;
  subscribeToProfile: (profileId: string) => void;
  unsubscribeFromProfile: (profileId: string) => void;
  clearNotifications: () => void;
  markAsRead: (index: number) => void;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
}

const WEBSOCKET_URL = import.meta.env.VITE_WS_URL || 'http://localhost:3001';

export function useWebSocket(userId?: string): UseWebSocketReturn {
  // Mock implementation for now - WebSocket functionality disabled
  const [isConnected, setIsConnected] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');

  // Mock connect function
  const connect = useCallback(() => {
    console.log('WebSocket mock: connect called');
    setConnectionStatus('disconnected'); // Keep as disconnected for now
  }, [userId]);

  // Mock disconnect function
  const disconnect = useCallback(() => {
    console.log('WebSocket mock: disconnect called');
    setIsConnected(false);
    setConnectionStatus('disconnected');
  }, []);

  // Mock sendMessage function
  const sendMessage = useCallback((event: string, data: any) => {
    console.log('WebSocket mock: sendMessage called', { event, data });
  }, []);

  // Mock subscribeToProfile function
  const subscribeToProfile = useCallback((profileId: string) => {
    console.log('WebSocket mock: subscribeToProfile called', { profileId });
  }, []);

  // Mock unsubscribeFromProfile function
  const unsubscribeFromProfile = useCallback((profileId: string) => {
    console.log('WebSocket mock: unsubscribeFromProfile called', { profileId });
  }, []);

  // Mock clearNotifications function
  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Mock markAsRead function
  const markAsRead = useCallback((index: number) => {
    setNotifications(prev =>
      prev.map((notification, i) =>
        i === index ? { ...notification, read: true } : notification
      )
    );
  }, []);

  // Mock useEffect hooks
  useEffect(() => {
    console.log('WebSocket mock: useEffect called');
    // Don't actually connect for now
  }, []);

  return {
    isConnected,
    notifications,
    sendMessage,
    subscribeToProfile,
    unsubscribeFromProfile,
    clearNotifications,
    markAsRead,
    connectionStatus
  };
}
