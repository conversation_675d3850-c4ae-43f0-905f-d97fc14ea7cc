import { useEffect, useRef, useState, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';

export interface Notification {
  type: 'profile_update' | 'analysis_complete' | 'system_alert' | 'user_message';
  title: string;
  message: string;
  data?: any;
  timestamp: string;
  userId?: string;
  profileId?: string;
}

export interface UseWebSocketReturn {
  isConnected: boolean;
  notifications: Notification[];
  sendMessage: (event: string, data: any) => void;
  subscribeToProfile: (profileId: string) => void;
  unsubscribeFromProfile: (profileId: string) => void;
  clearNotifications: () => void;
  markAsRead: (index: number) => void;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
}

const WEBSOCKET_URL = import.meta.env.VITE_WS_URL || 'http://localhost:3001';

export function useWebSocket(userId?: string): UseWebSocketReturn {
  const socketRef = useRef<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = useCallback(() => {
    if (socketRef.current?.connected) {
      return;
    }

    setConnectionStatus('connecting');
    
    const socket = io(WEBSOCKET_URL, {
      transports: ['websocket', 'polling'],
      timeout: 10000,
      reconnection: true,
      reconnectionAttempts: maxReconnectAttempts,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
    });

    socket.on('connect', () => {
      console.log('WebSocket connected');
      setIsConnected(true);
      setConnectionStatus('connected');
      reconnectAttempts.current = 0;

      // Authenticate if userId is provided
      if (userId) {
        socket.emit('authenticate', { userId });
      }

      // Send periodic ping to keep connection alive
      const pingInterval = setInterval(() => {
        if (socket.connected) {
          socket.emit('ping');
        } else {
          clearInterval(pingInterval);
        }
      }, 30000); // Ping every 30 seconds
    });

    socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      setIsConnected(false);
      setConnectionStatus('disconnected');

      // Auto-reconnect if disconnection was not intentional
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, don't reconnect
        return;
      }

      // Client-side reconnection logic
      if (reconnectAttempts.current < maxReconnectAttempts) {
        reconnectAttempts.current++;
        const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
        
        reconnectTimeoutRef.current = setTimeout(() => {
          console.log(`Attempting to reconnect (${reconnectAttempts.current}/${maxReconnectAttempts})`);
          connect();
        }, delay);
      } else {
        setConnectionStatus('error');
        console.error('Max reconnection attempts reached');
      }
    });

    socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      setConnectionStatus('error');
    });

    socket.on('notification', (notification: Notification) => {
      console.log('Received notification:', notification);
      setNotifications(prev => [notification, ...prev].slice(0, 50)); // Keep last 50 notifications
    });

    socket.on('profile_update', (update: Notification) => {
      console.log('Received profile update:', update);
      setNotifications(prev => [update, ...prev].slice(0, 50));
    });

    socket.on('pong', (data) => {
      console.log('Received pong:', data);
    });

    socketRef.current = socket;
  }, [userId]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }
    
    setIsConnected(false);
    setConnectionStatus('disconnected');
  }, []);

  const sendMessage = useCallback((event: string, data: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(event, data);
    } else {
      console.warn('WebSocket not connected, cannot send message');
    }
  }, []);

  const subscribeToProfile = useCallback((profileId: string) => {
    sendMessage('subscribe_profile', { profileId });
  }, [sendMessage]);

  const unsubscribeFromProfile = useCallback((profileId: string) => {
    sendMessage('unsubscribe_profile', { profileId });
  }, [sendMessage]);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  const markAsRead = useCallback((index: number) => {
    setNotifications(prev => 
      prev.map((notification, i) => 
        i === index ? { ...notification, read: true } : notification
      )
    );
  }, []);

  // Connect on mount
  useEffect(() => {
    connect();
    
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // Reconnect when userId changes
  useEffect(() => {
    if (socketRef.current?.connected && userId) {
      socketRef.current.emit('authenticate', { userId });
    }
  }, [userId]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);

  return {
    isConnected,
    notifications,
    sendMessage,
    subscribeToProfile,
    unsubscribeFromProfile,
    clearNotifications,
    markAsRead,
    connectionStatus
  };
}
