import { Server as SocketIOServer } from 'socket.io';
import { Server as HttpServer } from 'http';
import { apiLogger } from '../utils/logger.js';

export interface NotificationData {
  type: 'profile_update' | 'analysis_complete' | 'system_alert' | 'user_message';
  title: string;
  message: string;
  data?: any;
  timestamp: string;
  userId?: string;
  profileId?: string;
}

export class WebSocketService {
  private io: SocketIOServer;
  private connectedUsers = new Map<string, string>(); // socketId -> userId

  constructor(server: HttpServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:5173",
        methods: ["GET", "POST"]
      }
    });

    this.setupEventHandlers();
    apiLogger.info('WebSocket service initialized');
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      apiLogger.info('Client connected', { socketId: socket.id });

      // Handle user authentication/identification
      socket.on('authenticate', (data: { userId: string }) => {
        this.connectedUsers.set(socket.id, data.userId);
        socket.join(`user_${data.userId}`);
        apiLogger.info('User authenticated', { 
          socketId: socket.id, 
          userId: data.userId 
        });

        // Send welcome notification
        this.sendNotificationToSocket(socket.id, {
          type: 'system_alert',
          title: '连接成功',
          message: '实时通知已启用',
          timestamp: new Date().toISOString()
        });
      });

      // Handle profile subscription
      socket.on('subscribe_profile', (data: { profileId: string }) => {
        socket.join(`profile_${data.profileId}`);
        apiLogger.info('Subscribed to profile updates', { 
          socketId: socket.id, 
          profileId: data.profileId 
        });
      });

      // Handle profile unsubscription
      socket.on('unsubscribe_profile', (data: { profileId: string }) => {
        socket.leave(`profile_${data.profileId}`);
        apiLogger.info('Unsubscribed from profile updates', { 
          socketId: socket.id, 
          profileId: data.profileId 
        });
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        const userId = this.connectedUsers.get(socket.id);
        this.connectedUsers.delete(socket.id);
        apiLogger.info('Client disconnected', { 
          socketId: socket.id, 
          userId 
        });
      });

      // Handle ping for connection health
      socket.on('ping', () => {
        socket.emit('pong', { timestamp: new Date().toISOString() });
      });
    });
  }

  // Send notification to specific user
  public sendNotificationToUser(userId: string, notification: NotificationData) {
    this.io.to(`user_${userId}`).emit('notification', notification);
    apiLogger.info('Notification sent to user', { 
      userId, 
      type: notification.type,
      title: notification.title 
    });
  }

  // Send notification to specific socket
  public sendNotificationToSocket(socketId: string, notification: NotificationData) {
    this.io.to(socketId).emit('notification', notification);
    apiLogger.info('Notification sent to socket', { 
      socketId, 
      type: notification.type,
      title: notification.title 
    });
  }

  // Send notification to all users subscribed to a profile
  public sendProfileUpdate(profileId: string, notification: NotificationData) {
    this.io.to(`profile_${profileId}`).emit('profile_update', notification);
    apiLogger.info('Profile update sent', { 
      profileId, 
      type: notification.type,
      title: notification.title 
    });
  }

  // Broadcast notification to all connected users
  public broadcastNotification(notification: NotificationData) {
    this.io.emit('notification', notification);
    apiLogger.info('Notification broadcasted', { 
      type: notification.type,
      title: notification.title 
    });
  }

  // Send system status update
  public sendSystemStatus(status: {
    type: 'maintenance' | 'update' | 'alert';
    message: string;
    severity: 'info' | 'warning' | 'error';
  }) {
    const notification: NotificationData = {
      type: 'system_alert',
      title: '系统通知',
      message: status.message,
      data: { severity: status.severity },
      timestamp: new Date().toISOString()
    };

    this.broadcastNotification(notification);
  }

  // Get connected users count
  public getConnectedUsersCount(): number {
    return this.connectedUsers.size;
  }

  // Get users in specific room
  public getUsersInRoom(room: string): Promise<string[]> {
    return new Promise((resolve) => {
      this.io.in(room).allSockets().then((sockets) => {
        const users = Array.from(sockets).map(socketId => 
          this.connectedUsers.get(socketId)
        ).filter(Boolean) as string[];
        resolve(users);
      });
    });
  }

  // Simulate profile analysis progress updates
  public simulateProfileProgress(profileId: string, enterpriseName: string) {
    const steps = [
      { step: 1, title: '基本信息分析', progress: 20 },
      { step: 2, title: '行业环境分析', progress: 40 },
      { step: 3, title: '竞争对手分析', progress: 60 },
      { step: 4, title: 'SWOT分析', progress: 80 },
      { step: 5, title: '报告生成', progress: 100 }
    ];

    let currentStep = 0;
    const interval = setInterval(() => {
      if (currentStep >= steps.length) {
        clearInterval(interval);
        
        // Send completion notification
        this.sendProfileUpdate(profileId, {
          type: 'analysis_complete',
          title: '画像分析完成',
          message: `${enterpriseName} 的企业画像分析已完成`,
          data: { profileId, status: 'completed' },
          timestamp: new Date().toISOString(),
          profileId
        });
        return;
      }

      const step = steps[currentStep];
      this.sendProfileUpdate(profileId, {
        type: 'profile_update',
        title: '分析进度更新',
        message: `正在进行: ${step.title}`,
        data: { 
          profileId, 
          step: step.step, 
          progress: step.progress,
          status: 'in_progress'
        },
        timestamp: new Date().toISOString(),
        profileId
      });

      currentStep++;
    }, 3000); // Update every 3 seconds
  }
}

let websocketService: WebSocketService | null = null;

export function initializeWebSocket(server: HttpServer): WebSocketService {
  if (!websocketService) {
    websocketService = new WebSocketService(server);
  }
  return websocketService;
}

export function getWebSocketService(): WebSocketService | null {
  return websocketService;
}
