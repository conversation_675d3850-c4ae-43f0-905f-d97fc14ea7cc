import mongoose from 'mongoose';
import { Enterprise } from '../models/Enterprise.js';
import { Profile } from '../models/Profile.js';
import { dbLogger } from '../utils/logger.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const sampleEnterprises = [
  {
    name: '腾讯科技',
    industry: '互联网',
    size: '10000+人',
    stage: '成熟期',
    location: '深圳',
    revenue: '500亿+',
    employees: 85000,
    founded: 1998,
    status: '已分析',
    score: 95,
    description: '中国领先的互联网增值服务提供商，主要业务包括社交网络、游戏、金融科技等',
    website: 'https://www.tencent.com',
    contactInfo: {
      email: '<EMAIL>',
      phone: '0755-86013388',
      address: '深圳市南山区科技园'
    },
    businessInfo: {
      registrationNumber: '440301103212058',
      legalRepresentative: '马化腾',
      registeredCapital: '25亿元',
      businessScope: '互联网信息服务、游戏开发、金融科技'
    },
    analysisData: {
      riskLevel: 'low' as const,
      tags: ['互联网巨头', '社交网络', '游戏', '金融科技']
    }
  },
  {
    name: '阿里巴巴',
    industry: '电子商务',
    size: '10000+人',
    stage: '成熟期',
    location: '杭州',
    revenue: '700亿+',
    employees: 120000,
    founded: 1999,
    status: '已分析',
    score: 98,
    description: '全球领先的电子商务和云计算公司，业务涵盖电商、云计算、数字媒体等',
    website: 'https://www.alibaba.com',
    contactInfo: {
      email: '<EMAIL>',
      phone: '0571-85022088',
      address: '杭州市余杭区文一西路969号'
    },
    businessInfo: {
      registrationNumber: '330100000006395',
      legalRepresentative: '张勇',
      registeredCapital: '120亿元',
      businessScope: '电子商务、云计算、数字媒体'
    },
    analysisData: {
      riskLevel: 'low' as const,
      tags: ['电商巨头', '云计算', '数字支付', '物流']
    }
  },
  {
    name: '字节跳动',
    industry: '互联网',
    size: '10000+人',
    stage: '成长期',
    location: '北京',
    revenue: '300亿+',
    employees: 60000,
    founded: 2012,
    status: '已分析',
    score: 92,
    description: '全球化的移动互联网公司，旗下产品包括抖音、今日头条等',
    website: 'https://www.bytedance.com',
    contactInfo: {
      email: '<EMAIL>',
      phone: '010-82600000',
      address: '北京市海淀区知春路'
    },
    businessInfo: {
      registrationNumber: '110108014752259',
      legalRepresentative: '梁汝波',
      registeredCapital: '50亿元',
      businessScope: '移动互联网、短视频、信息流'
    },
    analysisData: {
      riskLevel: 'medium' as const,
      tags: ['短视频', '信息流', '全球化', '人工智能']
    }
  },
  {
    name: '美团',
    industry: '本地生活服务',
    size: '5000+人',
    stage: '成熟期',
    location: '北京',
    revenue: '200亿+',
    employees: 45000,
    founded: 2010,
    status: '已分析',
    score: 88,
    description: '中国领先的生活服务电子商务平台，提供外卖、酒店、旅游等服务',
    website: 'https://www.meituan.com',
    contactInfo: {
      email: '<EMAIL>',
      phone: '010-57846000',
      address: '北京市朝阳区望京东路6号'
    },
    businessInfo: {
      registrationNumber: '110105016297969',
      legalRepresentative: '王兴',
      registeredCapital: '30亿元',
      businessScope: '本地生活服务、外卖配送、酒店预订'
    },
    analysisData: {
      riskLevel: 'low' as const,
      tags: ['本地生活', '外卖', '酒店', '配送']
    }
  },
  {
    name: '小米科技',
    industry: '智能硬件',
    size: '1000-5000人',
    stage: '成熟期',
    location: '北京',
    revenue: '150亿+',
    employees: 25000,
    founded: 2010,
    status: '已分析',
    score: 85,
    description: '以智能手机、智能硬件和IoT平台为核心的互联网公司',
    website: 'https://www.mi.com',
    contactInfo: {
      email: '<EMAIL>',
      phone: '************',
      address: '北京市海淀区清河中街68号'
    },
    businessInfo: {
      registrationNumber: '110108013297236',
      legalRepresentative: '雷军',
      registeredCapital: '28亿元',
      businessScope: '智能手机、智能硬件、IoT平台'
    },
    analysisData: {
      riskLevel: 'medium' as const,
      tags: ['智能手机', '智能硬件', 'IoT', '生态链']
    }
  }
];

const sampleProfiles = [
  {
    name: '互联网成长期企业画像',
    description: '专注于寻找具有高成长潜力的互联网企业',
    targetConditions: {
      industry: ['互联网', '移动互联网', '电子商务'],
      stage: ['成长期', '成熟期'],
      size: ['101-500人', '501-1000人', '1000-5000人'],
      location: ['北京', '上海', '深圳', '杭州'],
      employees: {
        min: 100,
        max: 5000
      },
      founded: {
        min: 2010,
        max: 2020
      }
    },
    searchMethods: {
      dataSources: ['企查查', '天眼查', '官网'],
      keywords: ['互联网', 'SaaS', '人工智能', '大数据'],
      searchDepth: 'detailed' as const,
      autoUpdate: true,
      updateFrequency: 'weekly' as const
    },
    analysisConfig: {
      aiModel: 'deepseek-reasoner',
      analysisDepth: 'comprehensive' as const,
      includeRiskAssessment: true,
      includeCompetitorAnalysis: true
    },
    status: 'active' as const,
    createdBy: 'system'
  },
  {
    name: '智能硬件企业画像',
    description: '关注智能硬件和IoT领域的创新企业',
    targetConditions: {
      industry: ['智能硬件', '物联网', '人工智能'],
      stage: ['初创期', '成长期'],
      size: ['11-50人', '51-100人', '101-500人'],
      location: ['北京', '深圳', '上海', '成都'],
      employees: {
        min: 10,
        max: 500
      },
      founded: {
        min: 2015,
        max: 2023
      }
    },
    searchMethods: {
      dataSources: ['企查查', '天眼查', '启信宝'],
      keywords: ['智能硬件', 'IoT', '物联网', '传感器'],
      searchDepth: 'standard' as const,
      autoUpdate: false,
      updateFrequency: 'monthly' as const
    },
    analysisConfig: {
      aiModel: 'deepseek-reasoner',
      analysisDepth: 'standard' as const,
      includeRiskAssessment: true,
      includeCompetitorAnalysis: false
    },
    status: 'draft' as const,
    createdBy: 'system'
  }
];

async function seedDatabase() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/enterprise_profiler';
    await mongoose.connect(mongoUri);
    dbLogger.info('Connected to MongoDB for seeding');

    // Clear existing data
    await Enterprise.deleteMany({});
    await Profile.deleteMany({});
    dbLogger.info('Cleared existing data');

    // Insert sample enterprises
    const enterprises = await Enterprise.insertMany(sampleEnterprises);
    dbLogger.info(`Inserted ${enterprises.length} sample enterprises`);

    // Insert sample profiles
    const profiles = await Profile.insertMany(sampleProfiles);
    dbLogger.info(`Inserted ${profiles.length} sample profiles`);

    // Update profile results with enterprise references
    const activeProfile = profiles.find(p => p.status === 'active');
    if (activeProfile) {
      const matchingEnterprises = enterprises.filter(e => 
        e.industry === '互联网' && ['成长期', '成熟期'].includes(e.stage || '')
      );
      
      activeProfile.results = {
        totalMatches: matchingEnterprises.length,
        highPriorityMatches: matchingEnterprises.filter(e => (e.score || 0) >= 90).length,
        mediumPriorityMatches: matchingEnterprises.filter(e => (e.score || 0) >= 80 && (e.score || 0) < 90).length,
        lowPriorityMatches: matchingEnterprises.filter(e => (e.score || 0) < 80).length,
        analysisScore: 87.5,
        lastUpdated: new Date(),
        enterprises: matchingEnterprises.map(e => e._id as any)
      };
      
      await activeProfile.save();
      dbLogger.info('Updated profile results');
    }

    dbLogger.info('Database seeding completed successfully');
    process.exit(0);
  } catch (error) {
    dbLogger.error('Database seeding failed:', error);
    process.exit(1);
  }
}

// Run seeding if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedDatabase();
}

export { seedDatabase };
