import { useState, useEffect, useCallback } from 'react';
import { apiService } from '../services/api';
import { useCache } from './useCache';

interface Enterprise {
  _id: string;
  name: string;
  industry: string;
  stage?: string;
  location?: string;
  employees?: string;
  revenue?: string;
  founded?: string;
  score?: number;
}

interface EnterpriseFilters {
  industry?: string;
  stage?: string;
  location?: string;
  limit?: number;
  page?: number;
}

interface UseEnterprisesReturn {
  enterprises: Enterprise[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  hasMore: boolean;
  loadEnterprises: (filters?: EnterpriseFilters) => Promise<void>;
  loadMore: () => Promise<void>;
  refreshCache: () => void;
  clearCache: () => void;
}

export function useEnterprises(
  initialFilters: EnterpriseFilters = {}
): UseEnterprisesReturn {
  const [enterprises, setEnterprises] = useState<Enterprise[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<EnterpriseFilters>(initialFilters);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);

  // Create cache key based on filters
  const createCacheKey = useCallback((filterParams: EnterpriseFilters) => {
    const keyParts = [
      'enterprises',
      filterParams.industry || 'all',
      filterParams.stage || 'all',
      filterParams.location || 'all',
      filterParams.limit || 50,
      filterParams.page || 1
    ];
    return keyParts.join(':');
  }, []);

  const cacheKey = createCacheKey(filters);
  
  const { 
    data: cachedData, 
    set: setCachedData, 
    invalidate: invalidateCache 
  } = useCache<{
    enterprises: Enterprise[];
    totalCount: number;
    page: number;
  }>(cacheKey);

  const loadEnterprises = useCallback(async (newFilters?: EnterpriseFilters) => {
    const finalFilters = { ...filters, ...newFilters };
    const requestCacheKey = createCacheKey(finalFilters);
    
    // Check cache first
    const cached = cachedData;
    if (cached && !newFilters) {
      setEnterprises(cached.enterprises);
      setTotalCount(cached.totalCount);
      setCurrentPage(cached.page);
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      const response = await apiService.getEnterprises(finalFilters);
      
      if (response.success && response.data) {
        const { enterprises: newEnterprises, totalCount: newTotal } = response.data;
        
        setEnterprises(newEnterprises);
        setTotalCount(newTotal || newEnterprises.length);
        setCurrentPage(finalFilters.page || 1);
        setFilters(finalFilters);
        
        // Cache the results
        setCachedData({
          enterprises: newEnterprises,
          totalCount: newTotal || newEnterprises.length,
          page: finalFilters.page || 1
        });
      } else {
        setError(response.error || '加载企业数据失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '网络错误';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [filters, cachedData, setCachedData, createCacheKey]);

  const loadMore = useCallback(async () => {
    if (loading) return;
    
    const nextPage = currentPage + 1;
    const moreFilters = { ...filters, page: nextPage };
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiService.getEnterprises(moreFilters);
      
      if (response.success && response.data) {
        const { enterprises: moreEnterprises } = response.data;
        
        const updatedEnterprises = [...enterprises, ...moreEnterprises];
        setEnterprises(updatedEnterprises);
        setCurrentPage(nextPage);
        
        // Update cache with combined data
        setCachedData({
          enterprises: updatedEnterprises,
          totalCount,
          page: nextPage
        });
      } else {
        setError(response.error || '加载更多数据失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '网络错误';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [loading, currentPage, filters, enterprises, totalCount, setCachedData]);

  const refreshCache = useCallback(() => {
    invalidateCache();
    loadEnterprises();
  }, [invalidateCache, loadEnterprises]);

  const clearCache = useCallback(() => {
    invalidateCache();
    setEnterprises([]);
    setTotalCount(0);
    setCurrentPage(1);
  }, [invalidateCache]);

  // Load initial data
  useEffect(() => {
    loadEnterprises();
  }, []); // Only run once on mount

  const hasMore = enterprises.length < totalCount;

  return {
    enterprises,
    loading,
    error,
    totalCount,
    hasMore,
    loadEnterprises,
    loadMore,
    refreshCache,
    clearCache
  };
}
