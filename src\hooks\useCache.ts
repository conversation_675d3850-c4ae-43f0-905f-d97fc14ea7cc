import { useState, useEffect, useCallback } from 'react';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  maxSize?: number; // Maximum number of entries
}

class CacheManager {
  private cache = new Map<string, CacheEntry<any>>();
  private defaultTTL = 5 * 60 * 1000; // 5 minutes
  private maxSize = 100;

  constructor(options: CacheOptions = {}) {
    this.defaultTTL = options.ttl || this.defaultTTL;
    this.maxSize = options.maxSize || this.maxSize;
  }

  set<T>(key: string, data: T, ttl?: number): void {
    const now = Date.now();
    const expiry = now + (ttl || this.defaultTTL);

    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      data,
      timestamp: now,
      expiry
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    const now = Date.now();
    if (now > entry.expiry) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) {
      return false;
    }

    const now = Date.now();
    if (now > entry.expiry) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  // Clean expired entries
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiry) {
        this.cache.delete(key);
      }
    }
  }
}

// Global cache instance
const globalCache = new CacheManager();

// Cleanup expired entries every 5 minutes
setInterval(() => {
  globalCache.cleanup();
}, 5 * 60 * 1000);

export interface UseCacheReturn<T> {
  data: T | null;
  isStale: boolean;
  set: (data: T, ttl?: number) => void;
  invalidate: () => void;
  refresh: () => void;
}

export function useCache<T>(
  key: string,
  fetcher?: () => Promise<T>,
  options: CacheOptions & { autoRefresh?: boolean } = {}
): UseCacheReturn<T> {
  const [data, setData] = useState<T | null>(() => globalCache.get<T>(key));
  const [isStale, setIsStale] = useState(false);

  const set = useCallback((newData: T, ttl?: number) => {
    globalCache.set(key, newData, ttl);
    setData(newData);
    setIsStale(false);
  }, [key]);

  const invalidate = useCallback(() => {
    globalCache.delete(key);
    setData(null);
    setIsStale(true);
  }, [key]);

  const refresh = useCallback(async () => {
    if (fetcher) {
      try {
        const newData = await fetcher();
        set(newData);
      } catch (error) {
        console.error('Failed to refresh cache:', error);
        setIsStale(true);
      }
    }
  }, [fetcher, set]);

  useEffect(() => {
    const cachedData = globalCache.get<T>(key);
    if (cachedData) {
      setData(cachedData);
      setIsStale(false);
    } else if (options.autoRefresh && fetcher) {
      refresh();
    }
  }, [key, fetcher, refresh, options.autoRefresh]);

  return {
    data,
    isStale,
    set,
    invalidate,
    refresh
  };
}

export { globalCache };
