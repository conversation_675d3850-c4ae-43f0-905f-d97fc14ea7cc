import React, { useState, useEffect } from 'react';
import { MapPin, ArrowRight, Clock, CheckCircle, AlertCircle, TrendingUp, Target, Award, RefreshCw } from 'lucide-react';
import { apiService } from '../services/api';
import { useApiCall } from '../hooks/useApiCall';
import EnterpriseSelector from './common/EnterpriseSelector';
import ErrorMessage from './common/ErrorMessage';

const UserPathway = () => {
  const [selectedPathway, setSelectedPathway] = useState('awareness');
  const [selectedEnterprise, setSelectedEnterprise] = useState('');

  const {
    data: pathwayData,
    loading,
    error,
    execute: loadPathwayAnalysis
  } = useApiCall(apiService.getPathwayAnalysis);

  const handleEnterpriseChange = (enterpriseId: string) => {
    setSelectedEnterprise(enterpriseId);
    if (enterpriseId) {
      loadPathwayAnalysis(enterpriseId);
    }
  };

  const exportPathwayReport = async () => {
    if (!selectedEnterprise) {
      alert('请先选择企业');
      return;
    }
    await loadPathwayAnalysis(selectedEnterprise);
  };

  const pathwayStages = [
    {
      id: 'awareness',
      name: '需求认知',
      description: '企业意识到存在问题或机会',
      icon: AlertCircle,
      color: 'blue',
      duration: '1-2周',
      keyActions: ['问题识别', '初步调研', '内部讨论', '需求确认'],
      challenges: ['问题不够明确', '缺乏数据支撑', '内部认知不一致'],
      success_factors: ['清晰的问题定义', '充分的数据证据', '管理层支持']
    },
    {
      id: 'consideration',
      name: '方案评估',
      description: '寻找和评估可能的解决方案',
      icon: Target,
      color: 'green',
      duration: '2-4周',
      keyActions: ['方案调研', '供应商接触', '方案比较', '初步筛选'],
      challenges: ['方案过多难以选择', '缺乏专业判断', '内部意见分歧'],
      success_factors: ['明确的评估标准', '专业的技术支持', '充分的方案对比']
    },
    {
      id: 'decision',
      name: '决策制定',
      description: '最终决定采用某个解决方案',
      icon: CheckCircle,
      color: 'purple',
      duration: '1-3周',
      keyActions: ['方案确定', '预算审批', '合同谈判', '决策拍板'],
      challenges: ['预算限制', '风险担忧', '决策周期长'],
      success_factors: ['清晰的ROI计算', '风险控制方案', '决策层支持']
    },
    {
      id: 'implementation',
      name: '实施部署',
      description: '执行选定的解决方案',
      icon: TrendingUp,
      color: 'orange',
      duration: '4-12周',
      keyActions: ['项目启动', '系统部署', '人员培训', '试运行'],
      challenges: ['实施复杂', '人员抗拒', '系统集成难'],
      success_factors: ['详细的实施计划', '充分的培训支持', '及时的技术支持']
    },
    {
      id: 'evaluation',
      name: '效果评估',
      description: '评估解决方案的实际效果',
      icon: Award,
      color: 'red',
      duration: '2-6周',
      keyActions: ['效果测量', '数据分析', '问题反馈', '优化调整'],
      challenges: ['效果不明显', '数据不准确', '用户满意度低'],
      success_factors: ['明确的成功指标', '准确的数据收集', '持续的优化改进']
    }
  ];

  const renewalFactors = pathwayData?.pathway?.optimizationOpportunities?.map((opp, index) => ({
    factor: opp.opportunity,
    weight: Math.max(10, 35 - index * 5),
    description: opp.implementation
  })) || [
    { factor: '产品价值实现', weight: 30, description: '是否达到预期效果' },
    { factor: '用户满意度', weight: 25, description: '使用者的满意程度' },
    { factor: '技术支持质量', weight: 20, description: '售后服务水平' },
    { factor: '成本效益比', weight: 15, description: '投资回报率' },
    { factor: '替代方案成本', weight: 10, description: '更换供应商的成本' }
  ];

  // 使用API数据或默认数据
  const displayStages = pathwayData?.pathway?.stages?.map((stage, index) => ({
    id: stage.name.toLowerCase().replace(/\s+/g, '_'),
    name: stage.name,
    description: `${stage.name}阶段的关键活动和挑战`,
    icon: pathwayStages[index]?.icon || AlertCircle,
    color: pathwayStages[index]?.color || 'blue',
    duration: stage.duration,
    keyActions: stage.keyTriggers || [],
    challenges: stage.challenges || [],
    success_factors: stage.recommendations || []
  })) || pathwayStages;

  const selectedStage = displayStages.find(stage => stage.id === selectedPathway) || displayStages[0];
  const StageIcon = selectedStage?.icon || AlertCircle;

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'bg-blue-500',
      green: 'bg-green-500',
      purple: 'bg-purple-500',
      orange: 'bg-orange-500',
      red: 'bg-red-500',
    };
    return colors[color as keyof typeof colors] || 'bg-gray-500';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">用户路径分析</h1>
          <p className="text-slate-400">追踪企业从需求产生到价值实现的完整路径</p>
        </div>
        <button
          type="button"
          onClick={exportPathwayReport}
          disabled={loading || !selectedEnterprise}
          className="px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-slate-600 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"
        >
          {loading ? (
            <>
              <RefreshCw className="w-4 h-4 animate-spin" />
              <span>分析中...</span>
            </>
          ) : (
            <span>导出路径图</span>
          )}
        </button>
      </div>

      {/* Enterprise Selection */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <h2 className="text-lg font-semibold text-white mb-4">选择分析企业</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <EnterpriseSelector
            selectedEnterprise={selectedEnterprise}
            onEnterpriseChange={handleEnterpriseChange}
            label="企业选择"
            placeholder="请选择企业"
          />
          {selectedEnterprise && (
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                分析状态
              </label>
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${
                  pathwayData ? 'bg-green-400' : loading ? 'bg-yellow-400' : 'bg-slate-400'
                }`}></div>
                <span className="text-slate-300 text-sm">
                  {pathwayData ? '分析完成' : loading ? '分析中...' : '等待分析'}
                </span>
              </div>
            </div>
          )}
        </div>
        {error && (
          <ErrorMessage message={error} onRetry={() => loadPathwayAnalysis(selectedEnterprise)} />
        )}
      </div>

      {/* Pathway Timeline */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <h2 className="text-xl font-semibold text-white mb-6">用户决策路径</h2>
        
        <div className="flex items-center justify-between mb-8 overflow-x-auto">
          {displayStages.map((stage, index) => {
            const Icon = stage.icon;
            return (
              <div key={stage.id} className="flex items-center">
                <div
                  onClick={() => setSelectedPathway(stage.id)}
                  className={`flex flex-col items-center cursor-pointer transition-all duration-200 ${
                    selectedPathway === stage.id ? 'scale-110' : 'opacity-70 hover:opacity-100'
                  }`}
                >
                  <div className={`w-16 h-16 rounded-full ${getColorClasses(stage.color)} flex items-center justify-center mb-2`}>
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <span className="text-white font-medium text-sm text-center">{stage.name}</span>
                  <span className="text-slate-400 text-xs mt-1">{stage.duration}</span>
                </div>
                {index < displayStages.length - 1 && (
                  <ArrowRight className="w-6 h-6 text-slate-600 mx-4" />
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Selected Stage Details */}
      {selectedStage && (
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <div className="flex items-center space-x-4 mb-6">
            <div className={`w-16 h-16 rounded-xl ${getColorClasses(selectedStage.color)} flex items-center justify-center`}>
              <StageIcon className="w-8 h-8 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-semibold text-white">{selectedStage.name}</h2>
              <p className="text-slate-400 mt-1">{selectedStage.description}</p>
              <div className="flex items-center space-x-2 mt-2">
                <Clock className="w-4 h-4 text-slate-400" />
                <span className="text-slate-400 text-sm">预计用时：{selectedStage.duration}</span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Key Actions */}
            <div>
              <h3 className="text-lg font-medium text-white mb-4 flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-400" />
                <span>关键行动</span>
              </h3>
              <div className="space-y-2">
                {selectedStage.keyActions.map((action, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-slate-300 text-sm">{action}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Challenges */}
            <div>
              <h3 className="text-lg font-medium text-white mb-4 flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-red-400" />
                <span>主要挑战</span>
              </h3>
              <div className="space-y-2">
                {selectedStage.challenges.map((challenge, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                    <span className="text-slate-300 text-sm">{challenge}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Success Factors */}
            <div>
              <h3 className="text-lg font-medium text-white mb-4 flex items-center space-x-2">
                <Award className="w-5 h-5 text-blue-400" />
                <span>成功要素</span>
              </h3>
              <div className="space-y-2">
                {selectedStage.success_factors.map((factor, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span className="text-slate-300 text-sm">{factor}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Renewal Analysis */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <h2 className="text-xl font-semibold text-white mb-6 flex items-center space-x-2">
          <RefreshCw className="w-6 h-6 text-purple-400" />
          <span>续费决策分析</span>
        </h2>
        
        <div className="space-y-4">
          {renewalFactors.map((item, index) => (
            <div key={index} className="flex items-center space-between">
              <div className="flex items-center space-x-4 flex-1">
                <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                  {item.weight}%
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white font-medium">{item.factor}</span>
                  </div>
                  <p className="text-slate-400 text-sm">{item.description}</p>
                </div>
              </div>
              <div className="w-48 bg-slate-700 rounded-full h-2 ml-4">
                <div 
                  className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${item.weight * 3}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Pathway Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <h3 className="text-lg font-semibold text-white mb-2">平均转化周期</h3>
          <p className="text-3xl font-bold text-blue-400">8.5周</p>
          <p className="text-slate-400 text-sm mt-2">从需求认知到实施完成</p>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <h3 className="text-lg font-semibold text-white mb-2">整体转化率</h3>
          <p className="text-3xl font-bold text-green-400">24.5%</p>
          <p className="text-slate-400 text-sm mt-2">从初次接触到成功签约</p>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <h3 className="text-lg font-semibold text-white mb-2">客户满意度</h3>
          <p className="text-3xl font-bold text-purple-400">87%</p>
          <p className="text-slate-400 text-sm mt-2">实施完成后的满意度</p>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <h3 className="text-lg font-semibold text-white mb-2">续费率</h3>
          <p className="text-3xl font-bold text-orange-400">89%</p>
          <p className="text-slate-400 text-sm mt-2">第一年续费率</p>
        </div>
      </div>
    </div>
  );
};

export default UserPathway;