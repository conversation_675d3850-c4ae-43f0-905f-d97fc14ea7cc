import React, { createContext, useContext, useState } from 'react';
import { User, AuthState, LoginRequest, PermissionName } from '../../types/auth';

interface AuthContextType extends AuthState {
  login: (credentials: LoginRequest) => Promise<boolean>;
  logout: () => void;
  refreshAuth: () => Promise<boolean>;
  hasPermission: (permission: PermissionName, resource?: any) => boolean;
  hasRole: (roleName: string) => boolean;
  hasAnyPermission: (permissions: PermissionName[]) => boolean;
  hasAllPermissions: (permissions: PermissionName[]) => boolean;
  updateUserPreferences: (preferences: Partial<User['preferences']>) => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Mock user data
const mockUser: User = {
  id: '1',
  username: 'admin',
  email: '<EMAIL>',
  fullName: '系统管理员',
  roles: [{
    id: 'role_1',
    name: 'admin',
    displayName: '管理员',
    description: '系统管理员角色',
    permissions: [],
    isSystem: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }],
  permissions: [],
  isActive: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  preferences: {
    theme: 'dark',
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    notifications: {
      email: true,
      push: true,
      sms: false
    },
    dashboard: {
      layout: 'grid',
      widgets: []
    }
  }
};

export const MockAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [authState] = useState<AuthState>({
    isAuthenticated: true,
    user: mockUser,
    token: 'mock-token',
    refreshToken: 'mock-refresh-token',
    loading: false,
    error: null
  });

  const login = async (credentials: LoginRequest): Promise<boolean> => {
    return true;
  };

  const logout = () => {
    // Mock logout
  };

  const refreshAuth = async (): Promise<boolean> => {
    return true;
  };

  const hasPermission = (permission: PermissionName, resource?: any): boolean => {
    // Mock: admin has all permissions
    return true;
  };

  const hasRole = (roleName: string): boolean => {
    return authState.user?.roles.some(role => role.name === roleName) || false;
  };

  const hasAnyPermission = (permissions: PermissionName[]): boolean => {
    // Mock: admin has all permissions
    return true;
  };

  const hasAllPermissions = (permissions: PermissionName[]): boolean => {
    // Mock: admin has all permissions
    return true;
  };

  const updateUserPreferences = async (preferences: Partial<User['preferences']>): Promise<boolean> => {
    return true;
  };

  const contextValue: AuthContextType = {
    ...authState,
    login,
    logout,
    refreshAuth,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    updateUserPreferences
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};
