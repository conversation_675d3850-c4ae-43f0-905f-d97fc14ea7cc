import React, { useState, useEffect } from 'react';
import { Building2, Citrus as Industry, Calendar, TrendingUp, Star, Globe, MapPin, Users, DollarSign, RefreshCw, Plus, Search } from 'lucide-react';
import { useEnterprises } from '../hooks/useEnterprises';
import { useDebouncedCallback } from '../hooks/useDebounce';
import LoadingSpinner from './common/LoadingSpinner';
import ErrorMessage from './common/ErrorMessage';
import EmptyState from './common/EmptyState';

const EnterpriseAnalysis = () => {
  const [selectedEnterprise, setSelectedEnterprise] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    industry: '',
    stage: '',
    location: ''
  });

  const {
    enterprises,
    loading,
    error,
    totalCount,
    hasMore,
    loadEnterprises,
    loadMore,
    refreshCache
  } = useEnterprises({ limit: 20 });

  // 防抖的筛选函数
  const debouncedLoadEnterprises = useDebouncedCallback((filterParams) => {
    loadEnterprises(filterParams);
  }, 500);

  const handleFilterChange = (filterType: string, value: string) => {
    const newFilters = { ...filters, [filterType]: value };
    setFilters(newFilters);
    debouncedLoadEnterprises(newFilters);
  };

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    // 这里可以添加搜索逻辑
    if (value.trim()) {
      const searchFilters = { ...filters, search: value };
      debouncedLoadEnterprises(searchFilters);
    } else {
      debouncedLoadEnterprises(filters);
    }
  };

  const createNewEnterprise = () => {
    // TODO: 实现新增企业功能
    alert('新增企业功能开发中...');
  };

  const handleLoadMore = () => {
    if (hasMore && !loading) {
      loadMore();
    }
  };

  const analysisCategories = [
    {
      title: '企业性质',
      icon: Building2,
      color: 'blue',
      data: [
        { label: '民营企业', value: 65, color: 'bg-blue-500' },
        { label: '国有企业', value: 25, color: 'bg-green-500' },
        { label: '外资企业', value: 10, color: 'bg-purple-500' }
      ]
    },
    {
      title: '行业分布',
      icon: Industry,
      color: 'green',
      data: [
        { label: '互联网', value: 35, color: 'bg-blue-500' },
        { label: '制造业', value: 25, color: 'bg-green-500' },
        { label: '金融服务', value: 20, color: 'bg-purple-500' },
        { label: '其他', value: 20, color: 'bg-orange-500' }
      ]
    },
    {
      title: '发展阶段',
      icon: TrendingUp,
      color: 'purple',
      data: [
        { label: '成熟期', value: 45, color: 'bg-blue-500' },
        { label: '成长期', value: 35, color: 'bg-green-500' },
        { label: '初创期', value: 20, color: 'bg-purple-500' }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">企业信息分析</h1>
          <p className="text-slate-400">深度分析目标企业的基本信息和发展状况</p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            type="button"
            onClick={loadEnterprises}
            disabled={loading}
            className="px-4 py-2 bg-slate-600 hover:bg-slate-500 disabled:bg-slate-700 text-white rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>刷新</span>
          </button>
          <button
            type="button"
            onClick={createNewEnterprise}
            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>新增企业</span>
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <h2 className="text-lg font-semibold text-white mb-4">搜索和筛选</h2>

        {/* Search Bar */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-slate-300 mb-2">企业搜索</label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              placeholder="搜索企业名称、行业或地区..."
              className="w-full pl-10 pr-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400"
            />
          </div>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">行业</label>
            <input
              type="text"
              value={filters.industry}
              onChange={(e) => handleFilterChange('industry', e.target.value)}
              placeholder="输入行业名称"
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">发展阶段</label>
            <select
              value={filters.stage}
              onChange={(e) => handleFilterChange('stage', e.target.value)}
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white"
              title="选择发展阶段"
            >
              <option value="">全部阶段</option>
              <option value="初创期">初创期</option>
              <option value="成长期">成长期</option>
              <option value="成熟期">成熟期</option>
              <option value="转型期">转型期</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">地区</label>
            <input
              type="text"
              value={filters.location}
              onChange={(e) => handleFilterChange('location', e.target.value)}
              placeholder="输入地区名称"
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400"
            />
          </div>
        </div>

        {error && (
          <ErrorMessage
            message={error}
            onRetry={refreshCache}
            className="mt-4"
          />
        )}
      </div>

      {/* Analysis Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {analysisCategories.map((category) => {
          const Icon = category.icon;
          return (
            <div key={category.title} className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
              <div className="flex items-center space-x-3 mb-4">
                <Icon className="w-6 h-6 text-blue-400" />
                <h3 className="text-lg font-semibold text-white">{category.title}</h3>
              </div>
              <div className="space-y-3">
                {category.data.map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${item.color}`}></div>
                      <span className="text-slate-300 text-sm">{item.label}</span>
                    </div>
                    <span className="text-white font-medium">{item.value}%</span>
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>

      {/* Enterprise List */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50">
        <div className="p-6 border-b border-slate-700/50">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-white">目标企业列表</h2>
            <span className="text-slate-400 text-sm">
              共 {enterprises.length} 家企业
            </span>
          </div>
        </div>
        <div className="p-6">
          {loading && enterprises.length === 0 ? (
            <LoadingSpinner size="lg" text="加载企业数据中..." className="py-12" />
          ) : enterprises.length === 0 ? (
            <EmptyState
              icon={Building2}
              title="暂无企业数据"
              description="还没有企业数据，点击下方按钮添加第一家企业"
              action={{
                label: "添加第一家企业",
                onClick: createNewEnterprise
              }}
            />
          ) : (
            <>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {enterprises.map((enterprise) => (
                <div
                  key={enterprise._id}
                  className="bg-slate-700/50 rounded-lg p-5 border border-slate-600/50 hover:border-blue-500/50 transition-colors duration-200 cursor-pointer"
                  onClick={() => setSelectedEnterprise(enterprise)}
                >
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-white">{enterprise.name}</h3>
                    <div className="flex items-center space-x-2">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-sm text-slate-300">{enterprise.score || 'N/A'}</span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="flex items-center space-x-2">
                      <Industry className="w-4 h-4 text-blue-400" />
                      <span className="text-sm text-slate-300">{enterprise.industry}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Users className="w-4 h-4 text-green-400" />
                      <span className="text-sm text-slate-300">{enterprise.employees || 'N/A'}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4 text-purple-400" />
                      <span className="text-sm text-slate-300">{enterprise.location || 'N/A'}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <DollarSign className="w-4 h-4 text-orange-400" />
                      <span className="text-sm text-slate-300">{enterprise.revenue || 'N/A'}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      enterprise.stage === '成熟期' ? 'bg-blue-500/20 text-blue-300' :
                      enterprise.stage === '成长期' ? 'bg-green-500/20 text-green-300' :
                      enterprise.stage === '初创期' ? 'bg-purple-500/20 text-purple-300' :
                      'bg-slate-500/20 text-slate-300'
                    }`}>
                      {enterprise.stage || '未知'}
                    </span>
                    <span className="text-xs text-slate-400">
                      成立于 {enterprise.founded || 'N/A'}
                    </span>
                  </div>
                </div>
              ))}
              </div>

              {/* Load More Button */}
              {hasMore && (
                <div className="mt-6 text-center">
                  <button
                    type="button"
                    onClick={handleLoadMore}
                    disabled={loading}
                    className="px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-slate-600 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 mx-auto"
                  >
                    {loading ? (
                      <>
                        <RefreshCw className="w-4 h-4 animate-spin" />
                        <span>加载中...</span>
                      </>
                    ) : (
                      <span>加载更多 ({totalCount - enterprises.length} 家企业)</span>
                    )}
                  </button>
                </div>
              )}

              {/* Loading indicator for load more */}
              {loading && enterprises.length > 0 && (
                <div className="mt-4 text-center">
                  <LoadingSpinner size="sm" text="加载更多数据..." />
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Selected Enterprise Details */}
      {selectedEnterprise && (
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <h2 className="text-xl font-semibold text-white mb-4">企业详细信息</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium text-white mb-3">基本信息</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-slate-400">企业名称</span>
                  <span className="text-white">{selectedEnterprise.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">所属行业</span>
                  <span className="text-white">{selectedEnterprise.industry}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">企业规模</span>
                  <span className="text-white">{selectedEnterprise.size}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">发展阶段</span>
                  <span className="text-white">{selectedEnterprise.stage}</span>
                </div>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-medium text-white mb-3">经营状况</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-slate-400">营业收入</span>
                  <span className="text-white">{selectedEnterprise.revenue}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">员工人数</span>
                  <span className="text-white">{selectedEnterprise.employees}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">成立时间</span>
                  <span className="text-white">{selectedEnterprise.founded}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">行业地位</span>
                  <span className="text-white">{selectedEnterprise.status}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnterpriseAnalysis;