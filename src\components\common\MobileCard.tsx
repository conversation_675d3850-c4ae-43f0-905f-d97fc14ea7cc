import React from 'react';
import { ChevronRight, LucideIcon } from 'lucide-react';

interface MobileCardProps {
  title: string;
  subtitle?: string;
  icon?: LucideIcon;
  value?: string | number;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  onClick?: () => void;
  children?: React.ReactNode;
  className?: string;
  variant?: 'default' | 'compact' | 'detailed';
}

const MobileCard: React.FC<MobileCardProps> = ({
  title,
  subtitle,
  icon: Icon,
  value,
  trend,
  onClick,
  children,
  className = '',
  variant = 'default'
}) => {
  const isClickable = !!onClick;

  const baseClasses = `
    bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 
    transition-all duration-200
    ${isClickable ? 'cursor-pointer hover:bg-slate-700/50 hover:border-slate-600/50 active:scale-[0.98]' : ''}
    ${className}
  `;

  const content = (
    <>
      {variant === 'compact' ? (
        <div className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {Icon && (
                <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  <Icon className="w-5 h-5 text-blue-400" />
                </div>
              )}
              <div>
                <h3 className="text-sm font-medium text-white">{title}</h3>
                {subtitle && (
                  <p className="text-xs text-slate-400 mt-1">{subtitle}</p>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {value && (
                <span className="text-lg font-bold text-white">{value}</span>
              )}
              {isClickable && (
                <ChevronRight className="w-4 h-4 text-slate-400" />
              )}
            </div>
          </div>
        </div>
      ) : variant === 'detailed' ? (
        <div className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-3">
              {Icon && (
                <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  <Icon className="w-6 h-6 text-blue-400" />
                </div>
              )}
              <div>
                <h3 className="text-lg font-semibold text-white">{title}</h3>
                {subtitle && (
                  <p className="text-sm text-slate-400 mt-1">{subtitle}</p>
                )}
              </div>
            </div>
            {isClickable && (
              <ChevronRight className="w-5 h-5 text-slate-400" />
            )}
          </div>
          
          {value && (
            <div className="mb-4">
              <div className="flex items-baseline space-x-2">
                <span className="text-3xl font-bold text-white">{value}</span>
                {trend && (
                  <span className={`text-sm font-medium ${
                    trend.isPositive ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {trend.isPositive ? '+' : ''}{trend.value}%
                  </span>
                )}
              </div>
            </div>
          )}
          
          {children && (
            <div className="mt-4">
              {children}
            </div>
          )}
        </div>
      ) : (
        // Default variant
        <div className="p-5">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              {Icon && (
                <div className="w-11 h-11 bg-blue-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Icon className="w-5 h-5 text-blue-400" />
                </div>
              )}
              <div className="flex-1 min-w-0">
                <h3 className="text-base font-medium text-white truncate">{title}</h3>
                {subtitle && (
                  <p className="text-sm text-slate-400 mt-1 truncate">{subtitle}</p>
                )}
                {value && (
                  <div className="flex items-baseline space-x-2 mt-2">
                    <span className="text-xl font-bold text-white">{value}</span>
                    {trend && (
                      <span className={`text-xs font-medium ${
                        trend.isPositive ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {trend.isPositive ? '+' : ''}{trend.value}%
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
            {isClickable && (
              <ChevronRight className="w-5 h-5 text-slate-400 flex-shrink-0 ml-2" />
            )}
          </div>
          
          {children && (
            <div className="mt-4">
              {children}
            </div>
          )}
        </div>
      )}
    </>
  );

  return isClickable ? (
    <button
      type="button"
      onClick={onClick}
      className={`${baseClasses} w-full text-left`}
    >
      {content}
    </button>
  ) : (
    <div className={baseClasses}>
      {content}
    </div>
  );
};

export default MobileCard;
