import React, { useState, useEffect } from 'react';
import { Activity, Clock, CheckCircle, AlertTriangle, TrendingUp, Database } from 'lucide-react';
import { usePerformanceMonitor } from '../hooks/usePerformanceMonitor';

const PerformanceDashboard: React.FC = () => {
  const { getStats, getRecentMetrics, getSlowOperations } = usePerformanceMonitor();
  const [refreshKey, setRefreshKey] = useState(0);

  // Auto refresh every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setRefreshKey(prev => prev + 1);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const overallStats = getStats();
  const recentMetrics = getRecentMetrics(10);
  const slowOperations = getSlowOperations(1000);

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const getStatusColor = (success: boolean) => {
    return success ? 'text-green-400' : 'text-red-400';
  };

  const getPerformanceColor = (duration: number) => {
    if (duration < 500) return 'text-green-400';
    if (duration < 1000) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">性能监控</h2>
          <p className="text-slate-400">实时API性能和系统状态监控</p>
        </div>
        <div className="flex items-center space-x-2">
          <Activity className="w-5 h-5 text-green-400" />
          <span className="text-green-400 text-sm">实时监控中</span>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <div className="flex items-center space-x-3">
            <Database className="w-8 h-8 text-blue-400" />
            <div>
              <p className="text-slate-400 text-sm">总调用次数</p>
              <p className="text-2xl font-bold text-white">{overallStats.totalCalls}</p>
            </div>
          </div>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <div className="flex items-center space-x-3">
            <CheckCircle className="w-8 h-8 text-green-400" />
            <div>
              <p className="text-slate-400 text-sm">成功率</p>
              <p className="text-2xl font-bold text-white">{overallStats.successRate.toFixed(1)}%</p>
            </div>
          </div>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <div className="flex items-center space-x-3">
            <Clock className="w-8 h-8 text-yellow-400" />
            <div>
              <p className="text-slate-400 text-sm">平均响应时间</p>
              <p className="text-2xl font-bold text-white">{formatDuration(overallStats.averageDuration)}</p>
            </div>
          </div>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <div className="flex items-center space-x-3">
            <TrendingUp className="w-8 h-8 text-purple-400" />
            <div>
              <p className="text-slate-400 text-sm">最慢调用</p>
              <p className="text-2xl font-bold text-white">{formatDuration(overallStats.slowestCall)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent API Calls */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50">
        <div className="p-6 border-b border-slate-700/50">
          <h3 className="text-lg font-semibold text-white">最近API调用</h3>
        </div>
        <div className="p-6">
          {recentMetrics.length === 0 ? (
            <p className="text-slate-400 text-center py-8">暂无API调用记录</p>
          ) : (
            <div className="space-y-3">
              {recentMetrics.map((metric, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`w-2 h-2 rounded-full ${metric.success ? 'bg-green-400' : 'bg-red-400'}`}></div>
                    <span className="text-white font-medium">{metric.name}</span>
                    {metric.error && (
                      <span className="text-red-400 text-sm">({metric.error})</span>
                    )}
                  </div>
                  <div className="flex items-center space-x-4">
                    <span className={`text-sm ${getPerformanceColor(metric.duration)}`}>
                      {formatDuration(metric.duration)}
                    </span>
                    <span className="text-slate-400 text-sm">
                      {new Date(metric.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Slow Operations Alert */}
      {slowOperations.length > 0 && (
        <div className="bg-red-500/10 border border-red-500/50 rounded-xl p-6">
          <div className="flex items-center space-x-3 mb-4">
            <AlertTriangle className="w-6 h-6 text-red-400" />
            <h3 className="text-lg font-semibold text-red-300">慢查询警告</h3>
          </div>
          <p className="text-red-300 mb-4">检测到 {slowOperations.length} 个响应时间超过1秒的API调用</p>
          <div className="space-y-2">
            {slowOperations.slice(0, 5).map((metric, index) => (
              <div key={index} className="flex items-center justify-between p-2 bg-red-500/20 rounded">
                <span className="text-red-300">{metric.name}</span>
                <span className="text-red-400 font-medium">{formatDuration(metric.duration)}</span>
              </div>
            ))}
            {slowOperations.length > 5 && (
              <p className="text-red-400 text-sm">还有 {slowOperations.length - 5} 个慢查询...</p>
            )}
          </div>
        </div>
      )}

      {/* Performance Tips */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <h3 className="text-lg font-semibold text-white mb-4">性能优化建议</h3>
        <div className="space-y-3">
          {overallStats.averageDuration > 1000 && (
            <div className="flex items-start space-x-3 p-3 bg-yellow-500/10 border border-yellow-500/50 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-yellow-400 mt-0.5" />
              <div>
                <p className="text-yellow-300 font-medium">平均响应时间较慢</p>
                <p className="text-yellow-400 text-sm">建议检查网络连接或优化后端查询</p>
              </div>
            </div>
          )}
          
          {overallStats.successRate < 95 && (
            <div className="flex items-start space-x-3 p-3 bg-red-500/10 border border-red-500/50 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5" />
              <div>
                <p className="text-red-300 font-medium">API成功率偏低</p>
                <p className="text-red-400 text-sm">建议检查错误日志并修复相关问题</p>
              </div>
            </div>
          )}
          
          {overallStats.successRate >= 95 && overallStats.averageDuration <= 1000 && (
            <div className="flex items-start space-x-3 p-3 bg-green-500/10 border border-green-500/50 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <p className="text-green-300 font-medium">系统运行良好</p>
                <p className="text-green-400 text-sm">API响应时间和成功率都在正常范围内</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PerformanceDashboard;
