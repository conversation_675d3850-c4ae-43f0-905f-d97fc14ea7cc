import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, BarChart3, LineChart, Calendar, Filter, Download } from 'lucide-react';
import LoadingSpinner from './common/LoadingSpinner';
import ErrorMessage from './common/ErrorMessage';

interface TrendData {
  period: string;
  value: number;
  change: number;
  changePercent: number;
}

interface MarketTrend {
  id: string;
  name: string;
  category: string;
  currentValue: number;
  trend: 'up' | 'down' | 'stable';
  changePercent: number;
  data: TrendData[];
  forecast: TrendData[];
  insights: string[];
}

interface MarketTrendAnalysisProps {
  className?: string;
}

const MarketTrendAnalysis: React.FC<MarketTrendAnalysisProps> = ({ className = '' }) => {
  const [trends, setTrends] = useState<MarketTrend[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedPeriod, setSelectedPeriod] = useState('6m');
  const [selectedTrend, setSelectedTrend] = useState<MarketTrend | null>(null);

  const categories = [
    { value: 'all', label: '全部' },
    { value: 'technology', label: '科技' },
    { value: 'finance', label: '金融' },
    { value: 'manufacturing', label: '制造业' },
    { value: 'retail', label: '零售' },
    { value: 'healthcare', label: '医疗' }
  ];

  const periods = [
    { value: '3m', label: '最近3个月' },
    { value: '6m', label: '最近6个月' },
    { value: '1y', label: '最近1年' },
    { value: '2y', label: '最近2年' }
  ];

  useEffect(() => {
    loadTrendData();
  }, [selectedCategory, selectedPeriod]);

  const loadTrendData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Mock data - replace with actual API call
      const mockTrends: MarketTrend[] = [
        {
          id: '1',
          name: '人工智能投资热度',
          category: 'technology',
          currentValue: 892.5,
          trend: 'up',
          changePercent: 23.7,
          data: [
            { period: '2024-01', value: 720, change: 45, changePercent: 6.7 },
            { period: '2024-02', value: 765, change: 45, changePercent: 6.3 },
            { period: '2024-03', value: 810, change: 45, changePercent: 5.9 },
            { period: '2024-04', value: 845, change: 35, changePercent: 4.3 },
            { period: '2024-05', value: 870, change: 25, changePercent: 3.0 },
            { period: '2024-06', value: 892.5, change: 22.5, changePercent: 2.6 }
          ],
          forecast: [
            { period: '2024-07', value: 920, change: 27.5, changePercent: 3.1 },
            { period: '2024-08', value: 950, change: 30, changePercent: 3.3 },
            { period: '2024-09', value: 985, change: 35, changePercent: 3.7 }
          ],
          insights: [
            'AI投资在过去6个月持续增长，主要集中在大模型和自动化领域',
            '预计未来3个月增长趋势将继续，但增速可能放缓',
            '建议关注AI应用落地和商业化进展'
          ]
        },
        {
          id: '2',
          name: '新能源汽车市场份额',
          category: 'manufacturing',
          currentValue: 28.3,
          trend: 'up',
          changePercent: 15.2,
          data: [
            { period: '2024-01', value: 24.5, change: 1.2, changePercent: 5.1 },
            { period: '2024-02', value: 25.1, change: 0.6, changePercent: 2.4 },
            { period: '2024-03', value: 26.2, change: 1.1, changePercent: 4.4 },
            { period: '2024-04', value: 27.0, change: 0.8, changePercent: 3.1 },
            { period: '2024-05', value: 27.8, change: 0.8, changePercent: 3.0 },
            { period: '2024-06', value: 28.3, change: 0.5, changePercent: 1.8 }
          ],
          forecast: [
            { period: '2024-07', value: 29.1, change: 0.8, changePercent: 2.8 },
            { period: '2024-08', value: 30.0, change: 0.9, changePercent: 3.1 },
            { period: '2024-09', value: 31.2, change: 1.2, changePercent: 4.0 }
          ],
          insights: [
            '新能源汽车市场份额稳步提升，消费者接受度不断提高',
            '政策支持和技术进步是主要驱动因素',
            '预计年底市场份额将突破30%'
          ]
        },
        {
          id: '3',
          name: '数字支付普及率',
          category: 'finance',
          currentValue: 87.6,
          trend: 'stable',
          changePercent: 2.1,
          data: [
            { period: '2024-01', value: 85.8, change: 0.3, changePercent: 0.4 },
            { period: '2024-02', value: 86.1, change: 0.3, changePercent: 0.3 },
            { period: '2024-03', value: 86.5, change: 0.4, changePercent: 0.5 },
            { period: '2024-04', value: 87.0, change: 0.5, changePercent: 0.6 },
            { period: '2024-05', value: 87.3, change: 0.3, changePercent: 0.3 },
            { period: '2024-06', value: 87.6, change: 0.3, changePercent: 0.3 }
          ],
          forecast: [
            { period: '2024-07', value: 88.0, change: 0.4, changePercent: 0.5 },
            { period: '2024-08', value: 88.3, change: 0.3, changePercent: 0.3 },
            { period: '2024-09', value: 88.7, change: 0.4, changePercent: 0.5 }
          ],
          insights: [
            '数字支付普及率已达到较高水平，增长趋于平缓',
            '农村地区和老年群体仍有增长空间',
            '跨境支付和B2B支付是新的增长点'
          ]
        }
      ];

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const filteredTrends = selectedCategory === 'all' 
        ? mockTrends 
        : mockTrends.filter(trend => trend.category === selectedCategory);
      
      setTrends(filteredTrends);
    } catch (err) {
      setError('加载市场趋势数据失败');
    } finally {
      setLoading(false);
    }
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-400" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-400" />;
      default:
        return <BarChart3 className="w-4 h-4 text-slate-400" />;
    }
  };

  const getTrendColor = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return 'text-green-400';
      case 'down':
        return 'text-red-400';
      default:
        return 'text-slate-400';
    }
  };

  if (loading) {
    return (
      <div className={`bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50 ${className}`}>
        <LoadingSpinner size="md" text="加载市场趋势数据中..." />
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <LineChart className="w-6 h-6 text-blue-400" />
            <div>
              <h2 className="text-xl font-semibold text-white">市场趋势分析</h2>
              <p className="text-slate-400 text-sm mt-1">实时监控行业发展趋势和市场变化</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {categories.map(category => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
            
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {periods.map(period => (
                <option key={period.value} value={period.value}>
                  {period.label}
                </option>
              ))}
            </select>
            
            <button
              type="button"
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors duration-200 flex items-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>导出</span>
            </button>
          </div>
        </div>
      </div>

      {error && (
        <ErrorMessage message={error} onRetry={loadTrendData} />
      )}

      {/* Trends Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {trends.map((trend) => (
          <div
            key={trend.id}
            className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50 hover:border-slate-600/50 transition-colors duration-200 cursor-pointer"
            onClick={() => setSelectedTrend(trend)}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">{trend.name}</h3>
              {getTrendIcon(trend.trend)}
            </div>
            
            <div className="space-y-3">
              <div className="flex items-baseline space-x-2">
                <span className="text-2xl font-bold text-white">{trend.currentValue}</span>
                <span className={`text-sm font-medium ${getTrendColor(trend.trend)}`}>
                  {trend.changePercent > 0 ? '+' : ''}{trend.changePercent}%
                </span>
              </div>
              
              <div className="h-20 bg-slate-700/30 rounded-lg flex items-center justify-center">
                <span className="text-slate-400 text-sm">趋势图表</span>
              </div>
              
              <div className="text-slate-400 text-sm">
                {trend.insights[0]}
              </div>
            </div>
          </div>
        ))}
      </div>

      {trends.length === 0 && !loading && (
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-8 border border-slate-700/50 text-center">
          <LineChart className="w-12 h-12 text-slate-600 mx-auto mb-4" />
          <p className="text-slate-400">没有找到匹配的趋势数据</p>
        </div>
      )}

      {/* Detailed View Modal */}
      {selectedTrend && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-slate-800 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-slate-700">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold text-white">{selectedTrend.name}</h3>
                <button
                  type="button"
                  onClick={() => setSelectedTrend(null)}
                  className="text-slate-400 hover:text-white"
                >
                  ×
                </button>
              </div>
            </div>
            
            <div className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-lg font-medium text-white mb-3">历史数据</h4>
                  <div className="space-y-2">
                    {selectedTrend.data.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-slate-700/30 rounded">
                        <span className="text-slate-300">{item.period}</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-white">{item.value}</span>
                          <span className={`text-sm ${item.change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                            {item.change >= 0 ? '+' : ''}{item.change}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="text-lg font-medium text-white mb-3">预测数据</h4>
                  <div className="space-y-2">
                    {selectedTrend.forecast.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-blue-500/10 rounded">
                        <span className="text-slate-300">{item.period}</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-white">{item.value}</span>
                          <span className="text-blue-400 text-sm">
                            +{item.change}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              
              <div>
                <h4 className="text-lg font-medium text-white mb-3">分析洞察</h4>
                <div className="space-y-2">
                  {selectedTrend.insights.map((insight, index) => (
                    <div key={index} className="p-3 bg-slate-700/30 rounded-lg">
                      <p className="text-slate-300">{insight}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MarketTrendAnalysis;
