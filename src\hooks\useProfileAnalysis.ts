import { useState, useCallback, useEffect } from 'react';
import { apiService } from '../services/api';
import { useCache } from './useCache';

export interface ProfileStep {
  stepNumber: number;
  title: string;
  description: string;
  isCompleted: boolean;
  data?: any;
}

export interface ProfileProgress {
  currentStep: number;
  totalSteps: number;
  completedSteps: number[];
  stepData: Record<string, any>;
  completionRate: number;
}

export interface ProfileAnalysis {
  id: string;
  enterpriseId: string;
  enterpriseName: string;
  analysisType: string;
  status: 'created' | 'in_progress' | 'completed' | 'failed';
  progress: ProfileProgress;
  results?: any;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  estimatedCompletion?: string;
  reportUrl?: string;
}

export interface UseProfileAnalysisReturn {
  // State
  currentProfile: ProfileAnalysis | null;
  loading: boolean;
  error: string | null;
  
  // Actions
  createProfile: (enterpriseData: any, analysisType?: string) => Promise<ProfileAnalysis | null>;
  updateStep: (stepNumber: number, stepData: any, isCompleted?: boolean) => Promise<boolean>;
  getProgress: () => Promise<ProfileProgress | null>;
  completeAnalysis: () => Promise<boolean>;
  saveDraft: (draftData: any) => Promise<boolean>;
  loadDraft: () => Promise<any>;
  
  // Utilities
  getCurrentStep: () => ProfileStep | null;
  getStepProgress: () => number;
  canProceedToNext: () => boolean;
  reset: () => void;
}

const PROFILE_STEPS: Omit<ProfileStep, 'isCompleted' | 'data'>[] = [
  {
    stepNumber: 1,
    title: '基本信息',
    description: '填写企业基本信息和背景资料'
  },
  {
    stepNumber: 2,
    title: '行业分析',
    description: '分析企业所在行业的市场环境'
  },
  {
    stepNumber: 3,
    title: '竞争对手',
    description: '识别和分析主要竞争对手'
  },
  {
    stepNumber: 4,
    title: 'SWOT分析',
    description: '分析企业的优势、劣势、机会和威胁'
  },
  {
    stepNumber: 5,
    title: '总结报告',
    description: '生成完整的企业画像分析报告'
  }
];

export function useProfileAnalysis(): UseProfileAnalysisReturn {
  const [currentProfile, setCurrentProfile] = useState<ProfileAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Cache for draft data
  const { 
    data: cachedDraft, 
    set: setCachedDraft,
    invalidate: invalidateDraft 
  } = useCache<any>(`profile_draft_${currentProfile?.id || 'temp'}`);

  const createProfile = useCallback(async (
    enterpriseData: any, 
    analysisType: string = 'comprehensive'
  ): Promise<ProfileAnalysis | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiService.createProfileAnalysis(enterpriseData, analysisType);
      
      if (response.success && response.data) {
        setCurrentProfile(response.data);
        return response.data;
      } else {
        setError(response.error || '创建画像分析失败');
        return null;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建画像分析失败';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateStep = useCallback(async (
    stepNumber: number, 
    stepData: any, 
    isCompleted: boolean = false
  ): Promise<boolean> => {
    if (!currentProfile) {
      setError('没有活动的画像分析');
      return false;
    }

    setLoading(true);
    setError(null);
    
    try {
      const response = await apiService.updateProfileStep(
        currentProfile.id, 
        stepNumber, 
        stepData, 
        isCompleted
      );
      
      if (response.success && response.data) {
        setCurrentProfile(prev => prev ? {
          ...prev,
          ...response.data,
          updatedAt: new Date().toISOString()
        } : null);
        
        // Clear draft cache when step is completed
        if (isCompleted) {
          invalidateDraft();
        }
        
        return true;
      } else {
        setError(response.error || '更新步骤失败');
        return false;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '更新步骤失败';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, [currentProfile, invalidateDraft]);

  const getProgress = useCallback(async (): Promise<ProfileProgress | null> => {
    if (!currentProfile) {
      setError('没有活动的画像分析');
      return null;
    }

    try {
      const response = await apiService.getProfileProgress(currentProfile.id);
      
      if (response.success && response.data) {
        const progressData = response.data.progress;
        
        setCurrentProfile(prev => prev ? {
          ...prev,
          progress: progressData,
          status: response.data.status
        } : null);
        
        return progressData;
      } else {
        setError(response.error || '获取进度失败');
        return null;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取进度失败';
      setError(errorMessage);
      return null;
    }
  }, [currentProfile]);

  const completeAnalysis = useCallback(async (): Promise<boolean> => {
    if (!currentProfile) {
      setError('没有活动的画像分析');
      return false;
    }

    setLoading(true);
    setError(null);
    
    try {
      const response = await apiService.completeProfileAnalysis(currentProfile.id);
      
      if (response.success && response.data) {
        setCurrentProfile(prev => prev ? {
          ...prev,
          ...response.data,
          status: 'completed',
          completedAt: new Date().toISOString()
        } : null);
        
        // Clear draft cache
        invalidateDraft();
        
        return true;
      } else {
        setError(response.error || '完成分析失败');
        return false;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '完成分析失败';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, [currentProfile, invalidateDraft]);

  const saveDraft = useCallback(async (draftData: any): Promise<boolean> => {
    if (!currentProfile) {
      // Save to local cache if no active profile
      setCachedDraft(draftData);
      return true;
    }

    try {
      const response = await apiService.saveProfileDraft(currentProfile.id, draftData);
      
      if (response.success) {
        setCachedDraft(draftData);
        return true;
      } else {
        // Fallback to local cache
        setCachedDraft(draftData);
        return true;
      }
    } catch (err) {
      // Fallback to local cache
      setCachedDraft(draftData);
      return true;
    }
  }, [currentProfile, setCachedDraft]);

  const loadDraft = useCallback(async (): Promise<any> => {
    if (!currentProfile) {
      return cachedDraft;
    }

    try {
      const response = await apiService.getProfileDraft(currentProfile.id);
      
      if (response.success && response.data) {
        return response.data.draftData;
      } else {
        return cachedDraft;
      }
    } catch (err) {
      return cachedDraft;
    }
  }, [currentProfile, cachedDraft]);

  const getCurrentStep = useCallback((): ProfileStep | null => {
    if (!currentProfile) return null;
    
    const stepTemplate = PROFILE_STEPS.find(
      step => step.stepNumber === currentProfile.progress.currentStep
    );
    
    if (!stepTemplate) return null;
    
    return {
      ...stepTemplate,
      isCompleted: currentProfile.progress.completedSteps.includes(stepTemplate.stepNumber),
      data: currentProfile.progress.stepData[`step${stepTemplate.stepNumber}`]
    };
  }, [currentProfile]);

  const getStepProgress = useCallback((): number => {
    if (!currentProfile) return 0;
    return (currentProfile.progress.completedSteps.length / currentProfile.progress.totalSteps) * 100;
  }, [currentProfile]);

  const canProceedToNext = useCallback((): boolean => {
    if (!currentProfile) return false;
    const currentStepNumber = currentProfile.progress.currentStep;
    return currentProfile.progress.completedSteps.includes(currentStepNumber);
  }, [currentProfile]);

  const reset = useCallback(() => {
    setCurrentProfile(null);
    setError(null);
    setLoading(false);
    invalidateDraft();
  }, [invalidateDraft]);

  return {
    currentProfile,
    loading,
    error,
    createProfile,
    updateStep,
    getProgress,
    completeAnalysis,
    saveDraft,
    loadDraft,
    getCurrentStep,
    getStepProgress,
    canProceedToNext,
    reset
  };
}
