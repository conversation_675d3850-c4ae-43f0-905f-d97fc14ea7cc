import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: '企业画像师后端服务正在运行',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    mode: 'development',
    services: {
      ai: 'operational',
      database: 'connected',
      status: 'healthy'
    },
    project: 'enterprise-profiler'
  });
});

// Mock report templates
const mockTemplates = [
  {
    id: 'template_1',
    name: '企业画像分析报告',
    description: '全面的企业画像分析报告，包含基本信息、行业分析、竞争分析等',
    type: 'enterprise_profile',
    version: '1.0.0',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isDefault: true,
    sections: [
      {
        id: 'executive_summary',
        title: '执行摘要',
        type: 'text',
        order: 1,
        required: true,
        config: { fontSize: 14, alignment: 'left' }
      },
      {
        id: 'basic_info',
        title: '企业基本信息',
        type: 'table',
        order: 2,
        required: true,
        config: {
          columns: [
            { key: 'field', title: '字段', width: 30 },
            { key: 'value', title: '值', width: 70 }
          ],
          showHeader: true
        }
      }
    ]
  },
  {
    id: 'template_2',
    name: '角色分析报告',
    description: '企业决策角色分析报告',
    type: 'role_analysis',
    version: '1.0.0',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isDefault: true,
    sections: [
      {
        id: 'role_overview',
        title: '角色概览',
        type: 'metrics',
        order: 1,
        required: true,
        config: {}
      }
    ]
  }
];

// Report routes
app.get('/api/reports/templates', (req, res) => {
  console.log('GET /api/reports/templates');
  res.json({
    success: true,
    data: mockTemplates
  });
});

app.get('/api/reports/templates/:id', (req, res) => {
  console.log(`GET /api/reports/templates/${req.params.id}`);
  const template = mockTemplates.find(t => t.id === req.params.id);
  
  if (!template) {
    return res.status(404).json({
      success: false,
      message: 'Template not found'
    });
  }

  res.json({
    success: true,
    data: template
  });
});

app.post('/api/reports/generate', (req, res) => {
  console.log('POST /api/reports/generate', req.body);
  const reportId = `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  res.json({
    success: true,
    data: {
      reportId,
      status: 'processing',
      message: 'Report generation started'
    }
  });
});

app.get('/api/reports/:id/status', (req, res) => {
  console.log(`GET /api/reports/${req.params.id}/status`);
  res.json({
    success: true,
    data: {
      status: 'completed',
      progress: 100,
      downloadUrl: `/api/reports/${req.params.id}/download`,
      metadata: {
        fileSize: 1024000,
        generationTime: 3000
      }
    }
  });
});

app.get('/api/reports/:id/download', (req, res) => {
  console.log(`GET /api/reports/${req.params.id}/download`);
  res.setHeader('Content-Disposition', `attachment; filename="enterprise_report_${req.params.id}.html"`);
  res.setHeader('Content-Type', 'text/html');
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>企业分析报告</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        h1 { color: #3b82f6; border-bottom: 2px solid #3b82f6; padding-bottom: 10px; }
        h2 { color: #64748b; margin-top: 30px; }
        .section { margin: 20px 0; padding: 15px; background: #f8fafc; border-left: 4px solid #3b82f6; }
        .meta { color: #64748b; font-size: 14px; }
      </style>
    </head>
    <body>
      <h1>企业分析报告</h1>
      
      <div class="section">
        <h2>报告信息</h2>
        <p class="meta">报告ID: ${req.params.id}</p>
        <p class="meta">生成时间: ${new Date().toISOString()}</p>
        <p class="meta">报告类型: 企业画像分析</p>
      </div>

      <div class="section">
        <h2>执行摘要</h2>
        <p>本报告通过AI驱动的企业画像分析技术，对目标企业进行全面深入的分析，包括基本信息、行业环境、竞争态势、SWOT分析等多个维度。</p>
      </div>

      <div class="section">
        <h2>企业基本信息</h2>
        <table border="1" style="width: 100%; border-collapse: collapse;">
          <tr><td><strong>企业名称</strong></td><td>示例企业有限公司</td></tr>
          <tr><td><strong>成立时间</strong></td><td>2010年</td></tr>
          <tr><td><strong>注册资本</strong></td><td>1000万元</td></tr>
          <tr><td><strong>主营业务</strong></td><td>软件开发与技术服务</td></tr>
          <tr><td><strong>员工规模</strong></td><td>100-500人</td></tr>
        </table>
      </div>

      <div class="section">
        <h2>SWOT分析</h2>
        <h3>优势 (Strengths)</h3>
        <ul>
          <li>技术实力雄厚，拥有专业的研发团队</li>
          <li>产品质量稳定，客户满意度高</li>
          <li>市场定位清晰，品牌知名度较高</li>
        </ul>
        
        <h3>劣势 (Weaknesses)</h3>
        <ul>
          <li>资金规模相对较小，扩张能力有限</li>
          <li>国际化程度不高</li>
        </ul>
        
        <h3>机会 (Opportunities)</h3>
        <ul>
          <li>数字化转型带来的市场机遇</li>
          <li>政策支持科技创新企业发展</li>
        </ul>
        
        <h3>威胁 (Threats)</h3>
        <ul>
          <li>市场竞争日趋激烈</li>
          <li>技术更新换代速度加快</li>
        </ul>
      </div>

      <div class="section">
        <h2>建议与结论</h2>
        <p>基于以上分析，建议企业：</p>
        <ol>
          <li>继续加大研发投入，保持技术领先优势</li>
          <li>拓展融资渠道，支持业务快速发展</li>
          <li>加强国际化布局，开拓海外市场</li>
          <li>建立完善的风险管控体系</li>
        </ol>
      </div>

      <footer style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #e2e8f0; text-align: center; color: #64748b;">
        <p>本报告由企业分析平台自动生成 | 生成时间: ${new Date().toLocaleString()}</p>
      </footer>
    </body>
    </html>
  `);
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Simple server running on port ${PORT}`);
  console.log(`📊 Environment: development`);
  console.log(`🌐 Health check: http://localhost:${PORT}/health`);
  console.log(`📋 Report templates: http://localhost:${PORT}/api/reports/templates`);
});
