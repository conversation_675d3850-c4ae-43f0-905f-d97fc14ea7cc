import React, { useState, useEffect } from 'react';
import { TrendingUp, Building2, Users, Target, Award, Database, Brain, Zap } from 'lucide-react';
import { apiService } from '../services/api';
import { Industry<PERSON>ieChart, TrendLineChart, chartUtils } from './charts/ChartComponents';

const Dashboard = () => {
  const [stats, setStats] = useState([
    { name: '企业画像总数', value: '0', change: '+0%', icon: Building2, color: 'blue' },
    { name: '分析完成率', value: '0%', change: '+0%', icon: TrendingUp, color: 'green' },
    { name: '活跃用户', value: '0', change: '+0%', icon: Users, color: 'purple' },
    { name: '精准匹配率', value: '0%', change: '+0%', icon: Target, color: 'orange' },
  ]);
  const [enterprises, setEnterprises] = useState([]);
  const [loading, setLoading] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState({
    backend: false,
    ai: false,
    database: false
  });

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Check backend health
      const healthResponse = await apiService.healthCheck();
      if (healthResponse.success) {
        setConnectionStatus(prev => ({ ...prev, backend: true }));
      }

      // Check AI status
      const aiResponse = await apiService.getAIStatus();
      if (aiResponse.success) {
        setConnectionStatus(prev => ({ ...prev, ai: true }));
      }

      // Get enterprise stats
      const statsResponse = await apiService.getEnterpriseStats();
      if (statsResponse.success && statsResponse.data) {
        const data = statsResponse.data;
        setStats([
          { name: '企业画像总数', value: data.total.toString(), change: '+12%', icon: Building2, color: 'blue' },
          { name: '分析完成率', value: '89.5%', change: '+5.2%', icon: TrendingUp, color: 'green' },
          { name: '活跃用户', value: '324', change: '+8%', icon: Users, color: 'purple' },
          { name: '精准匹配率', value: `${Math.round(data.averageScore)}%`, change: '+2.1%', icon: Target, color: 'orange' },
        ]);
        setConnectionStatus(prev => ({ ...prev, database: true }));
      }

      // Get enterprise list for charts
      const enterpriseResponse = await apiService.getEnterprises({ limit: 50 });
      if (enterpriseResponse.success && enterpriseResponse.data) {
        setEnterprises(enterpriseResponse.data.enterprises);
      }
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const recentActivities = [
    { action: '新增企业画像', company: '腾讯科技', time: '2分钟前', status: 'success' },
    { action: '完成角色分析', company: '阿里巴巴', time: '15分钟前', status: 'success' },
    { action: '用户路径更新', company: '字节跳动', time: '1小时前', status: 'warning' },
    { action: '数据同步完成', company: '华为技术', time: '2小时前', status: 'success' },
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'bg-blue-500',
      green: 'bg-green-500',
      purple: 'bg-purple-500',
      orange: 'bg-orange-500',
    };
    return colors[color as keyof typeof colors] || 'bg-gray-500';
  };

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-4 lg:p-6 text-white">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <h1 className="text-xl lg:text-2xl font-bold mb-2">欢迎使用企业画像师</h1>
            <p className="text-blue-100 text-sm lg:text-base">通过AI驱动的企业画像分析，提升您的销售效率和决策准确性</p>
          </div>
          <div className="flex items-center space-x-2 flex-shrink-0 ml-4">
            <Brain className="w-8 h-8 lg:w-12 lg:h-12 text-blue-200" />
            <Zap className="w-6 h-6 lg:w-8 lg:h-8 text-yellow-300" />
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div key={stat.name} className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-4 lg:p-6 border border-slate-700/50">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-slate-400 text-xs lg:text-sm font-medium truncate">{stat.name}</p>
                  <p className="text-lg lg:text-2xl font-bold text-white mt-1">{stat.value}</p>
                  <p className="text-green-400 text-xs lg:text-sm mt-1">{stat.change}</p>
                </div>
                <div className={`w-10 h-10 lg:w-12 lg:h-12 rounded-lg ${getColorClasses(stat.color)} flex items-center justify-center flex-shrink-0 ml-2`}>
                  <Icon className="w-5 h-5 lg:w-6 lg:h-6 text-white" />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6 mb-6">
        {/* Industry Distribution Chart */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-4 lg:p-6 border border-slate-700/50">
          <h3 className="text-base lg:text-lg font-semibold text-white mb-4">行业分布</h3>
          {enterprises.length > 0 ? (
            <div className="h-64 lg:h-80">
              <IndustryPieChart
                data={chartUtils.generateIndustryDistribution(enterprises)}
                title=""
              />
            </div>
          ) : (
            <div className="h-64 lg:h-80 flex items-center justify-center text-slate-400">
              暂无数据
            </div>
          )}
        </div>

        {/* Growth Trend Chart */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-4 lg:p-6 border border-slate-700/50">
          <h3 className="text-base lg:text-lg font-semibold text-white mb-4">增长趋势</h3>
          <div className="h-64 lg:h-80">
            <TrendLineChart
              data={{
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{
                  label: '企业数量',
                  data: [20, 25, 30, 28, 35, 42],
                  borderColor: '#3b82f6',
                  backgroundColor: 'rgba(59, 130, 246, 0.1)',
                  fill: true,
                  tension: 0.4
                }]
              }}
              title=""
            />
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Recent Activities */}
        <div className="xl:col-span-2 bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <h2 className="text-xl font-semibold text-white mb-4">最近活动</h2>
          <div className="space-y-4">
            {recentActivities.map((activity, index) => (
              <div key={index} className="flex items-center justify-between py-3 border-b border-slate-700/50 last:border-b-0">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    activity.status === 'success' ? 'bg-green-400' : 
                    activity.status === 'warning' ? 'bg-yellow-400' : 'bg-red-400'
                  }`}></div>
                  <div>
                    <p className="text-white font-medium">{activity.action}</p>
                    <p className="text-slate-400 text-sm">{activity.company}</p>
                  </div>
                </div>
                <span className="text-slate-400 text-sm">{activity.time}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <h2 className="text-xl font-semibold text-white mb-4">快速操作</h2>
          <div className="space-y-3">
            <button type="button" className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors duration-200">
              新建企业画像
            </button>
            <button type="button" className="w-full bg-slate-700 hover:bg-slate-600 text-white py-3 px-4 rounded-lg font-medium transition-colors duration-200">
              批量导入企业
            </button>
            <button type="button" className="w-full bg-slate-700 hover:bg-slate-600 text-white py-3 px-4 rounded-lg font-medium transition-colors duration-200">
              导出分析报告
            </button>
            <button type="button" className="w-full bg-slate-700 hover:bg-slate-600 text-white py-3 px-4 rounded-lg font-medium transition-colors duration-200">
              数据库同步
            </button>
          </div>
        </div>
      </div>

      {/* System Status */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <h2 className="text-xl font-semibold text-white mb-4">系统状态</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-3">
            <Database className="w-5 h-5 text-green-400" />
            <span className="text-slate-400">MongoDB</span>
            <span className="text-green-400 text-sm">在线</span>
          </div>
          <div className="flex items-center space-x-3">
            <Database className="w-5 h-5 text-green-400" />
            <span className="text-slate-400">Neo4j</span>
            <span className="text-green-400 text-sm">在线</span>
          </div>
          <div className="flex items-center space-x-3">
            <Database className="w-5 h-5 text-green-400" />
            <span className="text-slate-400">PostgreSQL</span>
            <span className="text-green-400 text-sm">在线</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;