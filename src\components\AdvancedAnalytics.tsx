import React, { useState, useEffect } from 'react';
import { Bar<PERSON>hart3, TrendingUp, Pie<PERSON>hart, Activity, Target, Zap, Brain, Database } from 'lucide-react';
import { apiService } from '../services/api';
import LoadingSpinner from './common/LoadingSpinner';
import ErrorMessage from './common/ErrorMessage';

interface AnalyticsData {
  overview: {
    totalEnterprises: number;
    completedAnalyses: number;
    averageScore: number;
    growthRate: number;
  };
  industryDistribution: Array<{
    industry: string;
    count: number;
    percentage: number;
    averageScore: number;
  }>;
  performanceMetrics: Array<{
    metric: string;
    value: number;
    trend: 'up' | 'down' | 'stable';
    change: number;
  }>;
  riskAnalysis: {
    highRisk: number;
    mediumRisk: number;
    lowRisk: number;
    riskFactors: Array<{
      factor: string;
      impact: 'high' | 'medium' | 'low';
      frequency: number;
    }>;
  };
  competitiveAnalysis: {
    marketLeaders: Array<{
      name: string;
      marketShare: number;
      score: number;
    }>;
    competitiveAdvantages: string[];
    threats: string[];
  };
  predictiveInsights: Array<{
    insight: string;
    confidence: number;
    timeframe: string;
    impact: 'positive' | 'negative' | 'neutral';
  }>;
}

const AdvancedAnalytics: React.FC = () => {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState('30d');
  const [selectedDimension, setSelectedDimension] = useState('industry');

  useEffect(() => {
    loadAnalyticsData();
  }, [selectedTimeRange, selectedDimension]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Mock data - replace with actual API call
      const mockData: AnalyticsData = {
        overview: {
          totalEnterprises: 1247,
          completedAnalyses: 892,
          averageScore: 78.5,
          growthRate: 15.3
        },
        industryDistribution: [
          { industry: '制造业', count: 324, percentage: 26.0, averageScore: 82.1 },
          { industry: '科技', count: 298, percentage: 23.9, averageScore: 85.7 },
          { industry: '金融', count: 187, percentage: 15.0, averageScore: 79.3 },
          { industry: '零售', count: 156, percentage: 12.5, averageScore: 74.8 },
          { industry: '医疗', count: 134, percentage: 10.7, averageScore: 81.2 },
          { industry: '其他', count: 148, percentage: 11.9, averageScore: 76.4 }
        ],
        performanceMetrics: [
          { metric: '平均分析时间', value: 24.5, trend: 'down', change: -12.3 },
          { metric: '客户满意度', value: 94.2, trend: 'up', change: 3.1 },
          { metric: 'AI准确率', value: 89.7, trend: 'up', change: 5.8 },
          { metric: '报告生成速度', value: 87.3, trend: 'up', change: 8.2 }
        ],
        riskAnalysis: {
          highRisk: 89,
          mediumRisk: 234,
          lowRisk: 569,
          riskFactors: [
            { factor: '财务风险', impact: 'high', frequency: 23 },
            { factor: '市场风险', impact: 'medium', frequency: 45 },
            { factor: '技术风险', impact: 'medium', frequency: 31 },
            { factor: '合规风险', impact: 'high', frequency: 18 },
            { factor: '运营风险', impact: 'low', frequency: 67 }
          ]
        },
        competitiveAnalysis: {
          marketLeaders: [
            { name: '腾讯科技', marketShare: 18.5, score: 92.3 },
            { name: '阿里巴巴', marketShare: 16.2, score: 90.1 },
            { name: '字节跳动', marketShare: 12.8, score: 88.7 },
            { name: '美团', marketShare: 9.4, score: 85.2 },
            { name: '京东', marketShare: 8.1, score: 83.9 }
          ],
          competitiveAdvantages: [
            '技术创新能力强',
            '用户基础庞大',
            '生态系统完善',
            '资金实力雄厚',
            '品牌影响力大'
          ],
          threats: [
            '监管政策收紧',
            '国际竞争加剧',
            '技术迭代加速',
            '用户需求变化',
            '成本压力增大'
          ]
        },
        predictiveInsights: [
          {
            insight: '制造业数字化转型将在未来6个月内加速',
            confidence: 87.3,
            timeframe: '6个月',
            impact: 'positive'
          },
          {
            insight: '金融科技领域竞争将进一步加剧',
            confidence: 92.1,
            timeframe: '3个月',
            impact: 'negative'
          },
          {
            insight: '新能源行业投资热度将持续上升',
            confidence: 78.9,
            timeframe: '12个月',
            impact: 'positive'
          },
          {
            insight: '传统零售业面临更大转型压力',
            confidence: 84.6,
            timeframe: '9个月',
            impact: 'negative'
          }
        ]
      };

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      setData(mockData);
    } catch (err) {
      setError('加载分析数据失败');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <LoadingSpinner size="md" text="加载高级分析数据中..." />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <ErrorMessage message={error} onRetry={loadAnalyticsData} />
      </div>
    );
  }

  if (!data) return null;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Brain className="w-8 h-8 text-purple-400" />
            <div>
              <h1 className="text-2xl font-bold text-white">高级数据分析</h1>
              <p className="text-slate-400">多维度企业数据洞察与预测分析</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <select
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(e.target.value)}
              className="px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="7d">最近7天</option>
              <option value="30d">最近30天</option>
              <option value="90d">最近90天</option>
              <option value="1y">最近1年</option>
            </select>
            
            <select
              value={selectedDimension}
              onChange={(e) => setSelectedDimension(e.target.value)}
              className="px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="industry">按行业</option>
              <option value="region">按地区</option>
              <option value="size">按规模</option>
              <option value="performance">按绩效</option>
            </select>
          </div>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">企业总数</p>
              <p className="text-2xl font-bold text-white mt-1">{data.overview.totalEnterprises.toLocaleString()}</p>
            </div>
            <Database className="w-8 h-8 text-blue-400" />
          </div>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">完成分析</p>
              <p className="text-2xl font-bold text-white mt-1">{data.overview.completedAnalyses.toLocaleString()}</p>
            </div>
            <BarChart3 className="w-8 h-8 text-green-400" />
          </div>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">平均评分</p>
              <p className="text-2xl font-bold text-white mt-1">{data.overview.averageScore}</p>
            </div>
            <Target className="w-8 h-8 text-purple-400" />
          </div>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">增长率</p>
              <p className="text-2xl font-bold text-white mt-1">{data.overview.growthRate}%</p>
            </div>
            <TrendingUp className="w-8 h-8 text-orange-400" />
          </div>
        </div>
      </div>

      {/* Industry Distribution */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <PieChart className="w-5 h-5 mr-2 text-blue-400" />
          行业分布分析
        </h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            {data.industryDistribution.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-slate-700/50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 rounded-full bg-blue-400"></div>
                  <span className="text-white">{item.industry}</span>
                </div>
                <div className="text-right">
                  <div className="text-white font-medium">{item.count}</div>
                  <div className="text-slate-400 text-sm">{item.percentage}%</div>
                </div>
              </div>
            ))}
          </div>
          <div className="flex items-center justify-center">
            <div className="text-center">
              <div className="w-48 h-48 bg-slate-700/30 rounded-full flex items-center justify-center mb-4">
                <span className="text-slate-400">图表占位符</span>
              </div>
              <p className="text-slate-400 text-sm">行业分布饼图</p>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <Activity className="w-5 h-5 mr-2 text-green-400" />
          性能指标
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {data.performanceMetrics.map((metric, index) => (
            <div key={index} className="p-4 bg-slate-700/50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-slate-400 text-sm">{metric.metric}</span>
                <div className={`flex items-center space-x-1 ${
                  metric.trend === 'up' ? 'text-green-400' : 
                  metric.trend === 'down' ? 'text-red-400' : 'text-slate-400'
                }`}>
                  <TrendingUp className={`w-3 h-3 ${metric.trend === 'down' ? 'rotate-180' : ''}`} />
                  <span className="text-xs">{Math.abs(metric.change)}%</span>
                </div>
              </div>
              <div className="text-xl font-bold text-white">{metric.value}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Risk Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <h3 className="text-lg font-semibold text-white mb-4">风险分析</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-red-400">高风险</span>
              <span className="text-white font-medium">{data.riskAnalysis.highRisk}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-yellow-400">中风险</span>
              <span className="text-white font-medium">{data.riskAnalysis.mediumRisk}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-green-400">低风险</span>
              <span className="text-white font-medium">{data.riskAnalysis.lowRisk}</span>
            </div>
          </div>
          
          <div className="mt-6">
            <h4 className="text-white font-medium mb-3">主要风险因素</h4>
            <div className="space-y-2">
              {data.riskAnalysis.riskFactors.map((factor, index) => (
                <div key={index} className="flex items-center justify-between text-sm">
                  <span className="text-slate-300">{factor.factor}</span>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded text-xs ${
                      factor.impact === 'high' ? 'bg-red-500/20 text-red-400' :
                      factor.impact === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                      'bg-green-500/20 text-green-400'
                    }`}>
                      {factor.impact}
                    </span>
                    <span className="text-slate-400">{factor.frequency}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Predictive Insights */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Zap className="w-5 h-5 mr-2 text-yellow-400" />
            预测洞察
          </h3>
          <div className="space-y-4">
            {data.predictiveInsights.map((insight, index) => (
              <div key={index} className="p-4 bg-slate-700/50 rounded-lg">
                <div className="flex items-start justify-between mb-2">
                  <p className="text-white text-sm flex-1">{insight.insight}</p>
                  <span className={`ml-2 px-2 py-1 rounded text-xs ${
                    insight.impact === 'positive' ? 'bg-green-500/20 text-green-400' :
                    insight.impact === 'negative' ? 'bg-red-500/20 text-red-400' :
                    'bg-slate-500/20 text-slate-400'
                  }`}>
                    {insight.impact}
                  </span>
                </div>
                <div className="flex items-center justify-between text-xs text-slate-400">
                  <span>置信度: {insight.confidence}%</span>
                  <span>时间框架: {insight.timeframe}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedAnalytics;
