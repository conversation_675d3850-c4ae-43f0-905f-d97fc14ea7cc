import { 
  ReportTemplate, 
  ReportData, 
  ReportGenerationRequest, 
  ReportGenerationResponse,
  ReportExportOptions,
  DEFAULT_TEMPLATES
} from '../types/report';
import { apiService } from './api';

class ReportService {
  private templates: Map<string, ReportTemplate> = new Map();

  constructor() {
    this.initializeDefaultTemplates();
  }

  private initializeDefaultTemplates() {
    DEFAULT_TEMPLATES.forEach((template, index) => {
      const fullTemplate: ReportTemplate = {
        id: `template_${index + 1}`,
        version: '1.0.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        metadata: {
          author: '系统',
          company: '企业分析平台',
          pageSize: 'A4',
          orientation: 'portrait',
          margins: { top: 20, right: 20, bottom: 20, left: 20 },
          theme: {
            primaryColor: '#3b82f6',
            secondaryColor: '#64748b',
            fontFamily: 'Arial, sans-serif',
            fontSize: 12
          }
        },
        ...template
      } as ReportTemplate;
      
      this.templates.set(fullTemplate.id, fullTemplate);
    });
  }

  // Template management
  async getTemplates(): Promise<ReportTemplate[]> {
    try {
      const response = await apiService.getReportTemplates();
      if (response.success) {
        return response.data;
      }
    } catch (error) {
      console.warn('Failed to fetch templates from server, using local templates');
    }

    return Array.from(this.templates.values());
  }

  async getTemplate(templateId: string): Promise<ReportTemplate | null> {
    try {
      const response = await apiService.getReportTemplate(templateId);
      if (response.success) {
        return response.data;
      }
    } catch (error) {
      console.warn('Failed to fetch template from server, using local template');
    }

    return this.templates.get(templateId) || null;
  }

  async createTemplate(template: Omit<ReportTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<ReportTemplate> {
    const newTemplate: ReportTemplate = {
      ...template,
      id: `template_${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    try {
      const response = await apiService.request('/api/reports/templates', {
        method: 'POST',
        body: JSON.stringify(newTemplate)
      });
      
      if (response.success) {
        return response.data;
      }
    } catch (error) {
      console.warn('Failed to save template to server, saving locally');
    }
    
    this.templates.set(newTemplate.id, newTemplate);
    return newTemplate;
  }

  async updateTemplate(templateId: string, updates: Partial<ReportTemplate>): Promise<ReportTemplate | null> {
    const existingTemplate = await this.getTemplate(templateId);
    if (!existingTemplate) {
      throw new Error('Template not found');
    }

    const updatedTemplate: ReportTemplate = {
      ...existingTemplate,
      ...updates,
      updatedAt: new Date().toISOString()
    };

    try {
      const response = await apiService.request(`/api/reports/templates/${templateId}`, {
        method: 'PUT',
        body: JSON.stringify(updatedTemplate)
      });
      
      if (response.success) {
        return response.data;
      }
    } catch (error) {
      console.warn('Failed to update template on server, updating locally');
    }
    
    this.templates.set(templateId, updatedTemplate);
    return updatedTemplate;
  }

  async deleteTemplate(templateId: string): Promise<boolean> {
    try {
      const response = await apiService.request(`/api/reports/templates/${templateId}`, {
        method: 'DELETE'
      });
      
      if (response.success) {
        this.templates.delete(templateId);
        return true;
      }
    } catch (error) {
      console.warn('Failed to delete template from server, deleting locally');
    }
    
    this.templates.delete(templateId);
    return true;
  }

  // Report generation
  async generateReport(request: ReportGenerationRequest): Promise<ReportGenerationResponse> {
    try {
      const response = await apiService.generateReport(request);

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Report generation failed');
      }
    } catch (error) {
      console.error('Report generation failed:', error);
      throw error;
    }
  }

  async getReportStatus(reportId: string): Promise<{
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress: number;
    downloadUrl?: string;
    error?: string;
  }> {
    try {
      const response = await apiService.getReportStatus(reportId);
      if (response.success) {
        return response.data;
      }
    } catch (error) {
      console.error('Failed to get report status:', error);
    }

    return { status: 'failed', progress: 0, error: 'Status check failed' };
  }

  async downloadReport(reportId: string): Promise<Blob> {
    try {
      return await apiService.downloadReport(reportId);
    } catch (error) {
      console.error('Report download failed:', error);
      throw error;
    }
  }

  // Data preparation helpers
  prepareEnterpriseData(enterprise: any): Record<string, any> {
    return {
      basicInfo: {
        name: enterprise.name,
        industry: enterprise.industry,
        location: enterprise.location,
        employees: enterprise.employees,
        revenue: enterprise.revenue,
        founded: enterprise.founded,
        website: enterprise.website,
        description: enterprise.description
      },
      metrics: {
        score: enterprise.score || 0,
        growthRate: enterprise.growthRate || 0,
        marketShare: enterprise.marketShare || 0,
        profitability: enterprise.profitability || 0
      },
      analysis: {
        strengths: enterprise.strengths || [],
        weaknesses: enterprise.weaknesses || [],
        opportunities: enterprise.opportunities || [],
        threats: enterprise.threats || []
      }
    };
  }

  prepareRoleAnalysisData(roleAnalysis: any): Record<string, any> {
    return {
      roles: roleAnalysis.roles || [],
      decisionFlow: roleAnalysis.decisionFlow || {},
      influence: roleAnalysis.influence || {},
      recommendations: roleAnalysis.recommendations || []
    };
  }

  preparePathwayData(pathwayAnalysis: any): Record<string, any> {
    return {
      stages: pathwayAnalysis.pathway?.stages || [],
      touchpoints: pathwayAnalysis.pathway?.touchpoints || [],
      optimizations: pathwayAnalysis.pathway?.optimizationOpportunities || [],
      metrics: pathwayAnalysis.metrics || {}
    };
  }

  // Export format helpers
  getDefaultExportOptions(format: 'pdf' | 'excel' | 'word' | 'html'): ReportExportOptions {
    return {
      format,
      includeCharts: true,
      includeTables: true,
      includeImages: true,
      compression: format === 'pdf',
      watermark: format === 'pdf' ? '企业分析平台' : undefined
    };
  }

  // Validation
  validateTemplate(template: ReportTemplate): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!template.name?.trim()) {
      errors.push('模板名称不能为空');
    }

    if (!template.sections || template.sections.length === 0) {
      errors.push('模板必须包含至少一个部分');
    }

    if (template.sections) {
      const orders = template.sections.map(s => s.order);
      const uniqueOrders = new Set(orders);
      if (orders.length !== uniqueOrders.size) {
        errors.push('部分顺序不能重复');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  validateReportRequest(request: ReportGenerationRequest): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!request.templateId) {
      errors.push('模板ID不能为空');
    }

    if (!request.enterpriseId) {
      errors.push('企业ID不能为空');
    }

    if (!request.exportOptions) {
      errors.push('导出选项不能为空');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export const reportService = new ReportService();
