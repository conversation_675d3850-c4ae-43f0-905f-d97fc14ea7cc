import React, { useEffect } from 'react';
import { useApiCall } from '../../hooks/useApiCall';
import { apiService } from '../../services/api';
import LoadingSpinner from './LoadingSpinner';
import ErrorMessage from './ErrorMessage';

interface Enterprise {
  _id: string;
  name: string;
  industry: string;
  stage?: string;
  location?: string;
}

interface EnterpriseSelectorProps {
  selectedEnterprise: string;
  onEnterpriseChange: (enterpriseId: string) => void;
  label?: string;
  placeholder?: string;
  className?: string;
  autoLoadFirst?: boolean;
}

const EnterpriseSelector: React.FC<EnterpriseSelectorProps> = ({
  selectedEnterprise,
  onEnterpriseChange,
  label = '选择企业',
  placeholder = '请选择企业',
  className = '',
  autoLoadFirst = true
}) => {
  const {
    data: enterprises,
    loading,
    error,
    execute: loadEnterprises
  } = useApiCall<{ enterprises: Enterprise[] }>(apiService.getEnterprises);

  useEffect(() => {
    loadEnterprises({ limit: 50 });
  }, [loadEnterprises]);

  useEffect(() => {
    if (autoLoadFirst && enterprises?.enterprises?.length > 0 && !selectedEnterprise) {
      onEnterpriseChange(enterprises.enterprises[0]._id);
    }
  }, [enterprises, selectedEnterprise, onEnterpriseChange, autoLoadFirst]);

  if (loading) {
    return (
      <div className={className}>
        <label className="block text-sm font-medium text-slate-300 mb-2">
          {label}
        </label>
        <div className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg">
          <LoadingSpinner size="sm" text="加载企业列表..." />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={className}>
        <label className="block text-sm font-medium text-slate-300 mb-2">
          {label}
        </label>
        <ErrorMessage 
          message={error} 
          onRetry={() => loadEnterprises({ limit: 50 })}
          className="text-sm"
        />
      </div>
    );
  }

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-slate-300 mb-2">
        {label}
      </label>
      <select
        value={selectedEnterprise}
        onChange={(e) => onEnterpriseChange(e.target.value)}
        className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white"
        title={label}
      >
        <option value="">{placeholder}</option>
        {enterprises?.enterprises?.map((enterprise) => (
          <option key={enterprise._id} value={enterprise._id}>
            {enterprise.name} - {enterprise.industry}
          </option>
        ))}
      </select>
    </div>
  );
};

export default EnterpriseSelector;
