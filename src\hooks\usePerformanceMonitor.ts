import { useCallback, useRef } from 'react';

interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: number;
  success: boolean;
  error?: string;
}

interface PerformanceStats {
  totalCalls: number;
  successRate: number;
  averageDuration: number;
  slowestCall: number;
  fastestCall: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private maxMetrics = 1000; // Keep last 1000 metrics

  addMetric(metric: PerformanceMetric) {
    this.metrics.push(metric);
    
    // Keep only the last maxMetrics entries
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  getStats(name?: string): PerformanceStats {
    const filteredMetrics = name 
      ? this.metrics.filter(m => m.name === name)
      : this.metrics;

    if (filteredMetrics.length === 0) {
      return {
        totalCalls: 0,
        successRate: 0,
        averageDuration: 0,
        slowestCall: 0,
        fastestCall: 0
      };
    }

    const successfulCalls = filteredMetrics.filter(m => m.success);
    const durations = filteredMetrics.map(m => m.duration);

    return {
      totalCalls: filteredMetrics.length,
      successRate: (successfulCalls.length / filteredMetrics.length) * 100,
      averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      slowestCall: Math.max(...durations),
      fastestCall: Math.min(...durations)
    };
  }

  getRecentMetrics(count: number = 10): PerformanceMetric[] {
    return this.metrics.slice(-count);
  }

  clearMetrics() {
    this.metrics = [];
  }

  // Get metrics for a specific time period
  getMetricsInPeriod(startTime: number, endTime: number): PerformanceMetric[] {
    return this.metrics.filter(m => 
      m.timestamp >= startTime && m.timestamp <= endTime
    );
  }

  // Detect slow operations (above threshold)
  getSlowOperations(thresholdMs: number = 1000): PerformanceMetric[] {
    return this.metrics.filter(m => m.duration > thresholdMs);
  }
}

// Global performance monitor instance
const globalMonitor = new PerformanceMonitor();

export interface UsePerformanceMonitorReturn {
  measureAsync: <T>(name: string, operation: () => Promise<T>) => Promise<T>;
  measureSync: <T>(name: string, operation: () => T) => T;
  getStats: (name?: string) => PerformanceStats;
  getRecentMetrics: (count?: number) => PerformanceMetric[];
  clearMetrics: () => void;
  getSlowOperations: (thresholdMs?: number) => PerformanceMetric[];
}

export function usePerformanceMonitor(): UsePerformanceMonitorReturn {
  const measureAsync = useCallback(async <T>(
    name: string, 
    operation: () => Promise<T>
  ): Promise<T> => {
    const startTime = performance.now();
    const timestamp = Date.now();
    
    try {
      const result = await operation();
      const duration = performance.now() - startTime;
      
      globalMonitor.addMetric({
        name,
        duration,
        timestamp,
        success: true
      });
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      globalMonitor.addMetric({
        name,
        duration,
        timestamp,
        success: false,
        error: errorMessage
      });
      
      throw error;
    }
  }, []);

  const measureSync = useCallback(<T>(
    name: string, 
    operation: () => T
  ): T => {
    const startTime = performance.now();
    const timestamp = Date.now();
    
    try {
      const result = operation();
      const duration = performance.now() - startTime;
      
      globalMonitor.addMetric({
        name,
        duration,
        timestamp,
        success: true
      });
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      globalMonitor.addMetric({
        name,
        duration,
        timestamp,
        success: false,
        error: errorMessage
      });
      
      throw error;
    }
  }, []);

  const getStats = useCallback((name?: string) => {
    return globalMonitor.getStats(name);
  }, []);

  const getRecentMetrics = useCallback((count?: number) => {
    return globalMonitor.getRecentMetrics(count);
  }, []);

  const clearMetrics = useCallback(() => {
    globalMonitor.clearMetrics();
  }, []);

  const getSlowOperations = useCallback((thresholdMs?: number) => {
    return globalMonitor.getSlowOperations(thresholdMs);
  }, []);

  return {
    measureAsync,
    measureSync,
    getStats,
    getRecentMetrics,
    clearMetrics,
    getSlowOperations
  };
}

// Export the global monitor for direct access if needed
export { globalMonitor };
