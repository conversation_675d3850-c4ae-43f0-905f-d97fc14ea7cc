import React, { useState } from 'react';
import { Menu, X, Bell } from 'lucide-react';

interface NavigationItem {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
}

interface MobileNavigationProps {
  navigation: NavigationItem[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  notificationCenter?: React.ReactNode;
}

const MobileNavigation: React.FC<MobileNavigationProps> = ({
  navigation,
  activeTab,
  onTabChange,
  notificationCenter
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleTabChange = (tabId: string) => {
    onTabChange(tabId);
    setIsMenuOpen(false); // Close menu after selection
  };

  return (
    <>
      {/* Mobile Header */}
      <div className="lg:hidden bg-slate-800/80 backdrop-blur-sm border-b border-slate-700/50 px-4 py-3">
        <div className="flex items-center justify-between">
          <button
            type="button"
            onClick={toggleMenu}
            className="p-2 text-slate-400 hover:text-white transition-colors duration-200"
            aria-label="打开菜单"
          >
            <Menu className="w-6 h-6" />
          </button>
          
          <h1 className="text-lg font-semibold text-white truncate mx-4">
            {navigation.find(item => item.id === activeTab)?.name || '企业分析平台'}
          </h1>
          
          <div className="flex items-center space-x-2">
            {notificationCenter}
          </div>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMenuOpen && (
        <div className="lg:hidden fixed inset-0 z-50 bg-black bg-opacity-50" onClick={toggleMenu}>
          <div 
            className="fixed inset-y-0 left-0 w-80 max-w-[85vw] bg-slate-800 shadow-xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Menu Header */}
            <div className="flex items-center justify-between p-4 border-b border-slate-700">
              <h2 className="text-xl font-bold text-white">导航菜单</h2>
              <button
                type="button"
                onClick={toggleMenu}
                className="p-2 text-slate-400 hover:text-white transition-colors duration-200"
                aria-label="关闭菜单"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {/* Menu Items */}
            <nav className="p-4">
              <div className="space-y-2">
                {navigation.map((item) => {
                  const Icon = item.icon;
                  const isActive = activeTab === item.id;
                  
                  return (
                    <button
                      type="button"
                      key={item.id}
                      onClick={() => handleTabChange(item.id)}
                      className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                        isActive
                          ? 'bg-blue-600 text-white shadow-lg'
                          : 'text-slate-300 hover:text-white hover:bg-slate-700/50'
                      }`}
                    >
                      <Icon className="w-5 h-5 flex-shrink-0" />
                      <span className="font-medium">{item.name}</span>
                    </button>
                  );
                })}
              </div>
            </nav>

            {/* Menu Footer */}
            <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-slate-700">
              <div className="text-center">
                <p className="text-slate-400 text-sm">企业分析平台</p>
                <p className="text-slate-500 text-xs mt-1">v2.1.3</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Bottom Navigation for Mobile (Alternative) */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-slate-800/95 backdrop-blur-sm border-t border-slate-700/50 px-2 py-2 z-40">
        <div className="flex items-center justify-around">
          {navigation.slice(0, 5).map((item) => {
            const Icon = item.icon;
            const isActive = activeTab === item.id;
            
            return (
              <button
                type="button"
                key={item.id}
                onClick={() => onTabChange(item.id)}
                className={`flex flex-col items-center space-y-1 px-2 py-2 rounded-lg transition-all duration-200 ${
                  isActive
                    ? 'text-blue-400'
                    : 'text-slate-400 hover:text-white'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className="text-xs font-medium truncate max-w-[60px]">
                  {item.name}
                </span>
              </button>
            );
          })}
          
          {/* More button for additional items */}
          {navigation.length > 5 && (
            <button
              type="button"
              onClick={toggleMenu}
              className="flex flex-col items-center space-y-1 px-2 py-2 rounded-lg text-slate-400 hover:text-white transition-colors duration-200"
            >
              <Menu className="w-5 h-5" />
              <span className="text-xs font-medium">更多</span>
            </button>
          )}
        </div>
      </div>
    </>
  );
};

export default MobileNavigation;
