export interface User {
  id: string;
  username: string;
  email: string;
  fullName: string;
  avatar?: string;
  roles: Role[];
  permissions: Permission[];
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  preferences: UserPreferences;
  department?: string;
  position?: string;
}

export interface Role {
  id: string;
  name: string;
  displayName: string;
  description: string;
  permissions: Permission[];
  isSystem: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Permission {
  id: string;
  name: string;
  displayName: string;
  description: string;
  resource: string;
  action: string;
  conditions?: PermissionCondition[];
}

export interface PermissionCondition {
  field: string;
  operator: 'eq' | 'ne' | 'in' | 'nin' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'startsWith' | 'endsWith';
  value: any;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  timezone: string;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  dashboard: {
    layout: 'grid' | 'list';
    widgets: string[];
  };
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  loading: boolean;
  error: string | null;
}

export interface LoginRequest {
  username: string;
  password: string;
  rememberMe?: boolean;
}

export interface LoginResponse {
  success: boolean;
  user: User;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  fullName: string;
  department?: string;
  position?: string;
}

// Predefined system roles
export const SYSTEM_ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  ANALYST: 'analyst',
  VIEWER: 'viewer',
  GUEST: 'guest'
} as const;

// Predefined permissions
export const PERMISSIONS = {
  // Enterprise management
  ENTERPRISE_VIEW: 'enterprise:view',
  ENTERPRISE_CREATE: 'enterprise:create',
  ENTERPRISE_EDIT: 'enterprise:edit',
  ENTERPRISE_DELETE: 'enterprise:delete',
  ENTERPRISE_EXPORT: 'enterprise:export',

  // Analysis
  ANALYSIS_VIEW: 'analysis:view',
  ANALYSIS_CREATE: 'analysis:create',
  ANALYSIS_EDIT: 'analysis:edit',
  ANALYSIS_DELETE: 'analysis:delete',
  ANALYSIS_EXPORT: 'analysis:export',

  // Reports
  REPORT_VIEW: 'report:view',
  REPORT_CREATE: 'report:create',
  REPORT_EDIT: 'report:edit',
  REPORT_DELETE: 'report:delete',
  REPORT_EXPORT: 'report:export',
  REPORT_TEMPLATE_MANAGE: 'report:template:manage',

  // User management
  USER_VIEW: 'user:view',
  USER_CREATE: 'user:create',
  USER_EDIT: 'user:edit',
  USER_DELETE: 'user:delete',
  USER_ROLE_ASSIGN: 'user:role:assign',

  // Role management
  ROLE_VIEW: 'role:view',
  ROLE_CREATE: 'role:create',
  ROLE_EDIT: 'role:edit',
  ROLE_DELETE: 'role:delete',
  ROLE_PERMISSION_ASSIGN: 'role:permission:assign',

  // System settings
  SYSTEM_SETTINGS_VIEW: 'system:settings:view',
  SYSTEM_SETTINGS_EDIT: 'system:settings:edit',
  SYSTEM_LOGS_VIEW: 'system:logs:view',
  SYSTEM_BACKUP: 'system:backup',

  // AI services
  AI_SERVICE_USE: 'ai:service:use',
  AI_SERVICE_CONFIGURE: 'ai:service:configure',

  // Data visualization
  VISUALIZATION_VIEW: 'visualization:view',
  VISUALIZATION_CREATE: 'visualization:create',
  VISUALIZATION_EDIT: 'visualization:edit',
  VISUALIZATION_DELETE: 'visualization:delete'
} as const;

// Default role configurations
export const DEFAULT_ROLE_PERMISSIONS = {
  [SYSTEM_ROLES.SUPER_ADMIN]: Object.values(PERMISSIONS),
  
  [SYSTEM_ROLES.ADMIN]: [
    PERMISSIONS.ENTERPRISE_VIEW,
    PERMISSIONS.ENTERPRISE_CREATE,
    PERMISSIONS.ENTERPRISE_EDIT,
    PERMISSIONS.ENTERPRISE_EXPORT,
    PERMISSIONS.ANALYSIS_VIEW,
    PERMISSIONS.ANALYSIS_CREATE,
    PERMISSIONS.ANALYSIS_EDIT,
    PERMISSIONS.ANALYSIS_EXPORT,
    PERMISSIONS.REPORT_VIEW,
    PERMISSIONS.REPORT_CREATE,
    PERMISSIONS.REPORT_EDIT,
    PERMISSIONS.REPORT_EXPORT,
    PERMISSIONS.REPORT_TEMPLATE_MANAGE,
    PERMISSIONS.USER_VIEW,
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_EDIT,
    PERMISSIONS.USER_ROLE_ASSIGN,
    PERMISSIONS.ROLE_VIEW,
    PERMISSIONS.SYSTEM_SETTINGS_VIEW,
    PERMISSIONS.AI_SERVICE_USE,
    PERMISSIONS.VISUALIZATION_VIEW,
    PERMISSIONS.VISUALIZATION_CREATE,
    PERMISSIONS.VISUALIZATION_EDIT
  ],
  
  [SYSTEM_ROLES.ANALYST]: [
    PERMISSIONS.ENTERPRISE_VIEW,
    PERMISSIONS.ENTERPRISE_CREATE,
    PERMISSIONS.ENTERPRISE_EDIT,
    PERMISSIONS.ENTERPRISE_EXPORT,
    PERMISSIONS.ANALYSIS_VIEW,
    PERMISSIONS.ANALYSIS_CREATE,
    PERMISSIONS.ANALYSIS_EDIT,
    PERMISSIONS.ANALYSIS_EXPORT,
    PERMISSIONS.REPORT_VIEW,
    PERMISSIONS.REPORT_CREATE,
    PERMISSIONS.REPORT_EDIT,
    PERMISSIONS.REPORT_EXPORT,
    PERMISSIONS.AI_SERVICE_USE,
    PERMISSIONS.VISUALIZATION_VIEW,
    PERMISSIONS.VISUALIZATION_CREATE,
    PERMISSIONS.VISUALIZATION_EDIT
  ],
  
  [SYSTEM_ROLES.VIEWER]: [
    PERMISSIONS.ENTERPRISE_VIEW,
    PERMISSIONS.ANALYSIS_VIEW,
    PERMISSIONS.REPORT_VIEW,
    PERMISSIONS.VISUALIZATION_VIEW
  ],
  
  [SYSTEM_ROLES.GUEST]: [
    PERMISSIONS.ENTERPRISE_VIEW,
    PERMISSIONS.ANALYSIS_VIEW,
    PERMISSIONS.REPORT_VIEW
  ]
};

export type SystemRole = typeof SYSTEM_ROLES[keyof typeof SYSTEM_ROLES];
export type PermissionName = typeof PERMISSIONS[keyof typeof PERMISSIONS];
