import React, { useState, useEffect } from 'react';
import { FileText, Download, Setting<PERSON>, Eye, RefreshCw, CheckCircle, AlertCircle, X } from 'lucide-react';
import { reportService } from '../services/reportService';
import { ReportTemplate, ReportGenerationRequest, ReportExportOptions } from '../types/report';
import LoadingSpinner from './common/LoadingSpinner';
import ErrorMessage from './common/ErrorMessage';

interface ReportGeneratorProps {
  enterpriseId?: string;
  enterpriseName?: string;
  onClose?: () => void;
}

const ReportGenerator: React.FC<ReportGeneratorProps> = ({
  enterpriseId,
  enterpriseName,
  onClose
}) => {
  const [templates, setTemplates] = useState<ReportTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const [exportOptions, setExportOptions] = useState<ReportExportOptions>({
    format: 'pdf',
    includeCharts: true,
    includeTables: true,
    includeImages: true,
    compression: true
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [generationStatus, setGenerationStatus] = useState<{
    reportId?: string;
    status: 'idle' | 'generating' | 'completed' | 'failed';
    progress: number;
    downloadUrl?: string;
  }>({ status: 'idle', progress: 0 });

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      const templatesData = await reportService.getTemplates();
      setTemplates(templatesData);
      if (templatesData.length > 0) {
        setSelectedTemplate(templatesData[0]);
      }
    } catch (err) {
      setError('加载模板失败');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateReport = async () => {
    if (!selectedTemplate || !enterpriseId) {
      setError('请选择模板和企业');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setGenerationStatus({ status: 'generating', progress: 0 });

      const request: ReportGenerationRequest = {
        templateId: selectedTemplate.id,
        enterpriseId,
        exportOptions
      };

      const response = await reportService.generateReport(request);
      
      if (response.success) {
        setGenerationStatus({
          reportId: response.reportId,
          status: 'generating',
          progress: 10
        });

        // Poll for status updates
        pollReportStatus(response.reportId);
      } else {
        throw new Error(response.error || '报告生成失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '报告生成失败');
      setGenerationStatus({ status: 'failed', progress: 0 });
    } finally {
      setLoading(false);
    }
  };

  const pollReportStatus = async (reportId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const status = await reportService.getReportStatus(reportId);
        
        setGenerationStatus({
          reportId,
          status: status.status === 'completed' ? 'completed' : 'generating',
          progress: status.progress,
          downloadUrl: status.downloadUrl
        });

        if (status.status === 'completed' || status.status === 'failed') {
          clearInterval(pollInterval);
          if (status.status === 'failed') {
            setError(status.error || '报告生成失败');
            setGenerationStatus({ status: 'failed', progress: 0 });
          }
        }
      } catch (err) {
        clearInterval(pollInterval);
        setError('获取报告状态失败');
        setGenerationStatus({ status: 'failed', progress: 0 });
      }
    }, 1000);

    // Cleanup after 5 minutes
    setTimeout(() => {
      clearInterval(pollInterval);
    }, 5 * 60 * 1000);
  };

  const handleDownload = async () => {
    if (!generationStatus.reportId) return;

    try {
      const blob = await reportService.downloadReport(generationStatus.reportId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `enterprise_report_${enterpriseId}.${exportOptions.format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      setError('下载报告失败');
    }
  };

  const handlePreview = () => {
    // TODO: Implement report preview
    alert('预览功能即将推出');
  };

  if (loading && templates.length === 0) {
    return (
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <LoadingSpinner size="md" text="加载报告模板中..." />
      </div>
    );
  }

  return (
    <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-slate-700/50">
        <div className="flex items-center space-x-3">
          <FileText className="w-6 h-6 text-blue-400" />
          <div>
            <h2 className="text-xl font-semibold text-white">报告生成</h2>
            {enterpriseName && (
              <p className="text-slate-400 text-sm mt-1">为 {enterpriseName} 生成分析报告</p>
            )}
          </div>
        </div>
        {onClose && (
          <button
            type="button"
            onClick={onClose}
            className="p-2 text-slate-400 hover:text-white transition-colors duration-200"
          >
            <X className="w-5 h-5" />
          </button>
        )}
      </div>

      <div className="p-6 space-y-6">
        {error && (
          <ErrorMessage message={error} onRetry={() => setError(null)} />
        )}

        {/* Template Selection */}
        <div>
          <label className="block text-sm font-medium text-white mb-3">选择报告模板</label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {templates.map((template) => (
              <button
                key={template.id}
                type="button"
                onClick={() => setSelectedTemplate(template)}
                className={`p-4 rounded-lg border transition-all duration-200 text-left ${
                  selectedTemplate?.id === template.id
                    ? 'border-blue-500 bg-blue-500/10'
                    : 'border-slate-600 hover:border-slate-500 bg-slate-700/50'
                }`}
              >
                <h3 className="font-medium text-white mb-2">{template.name}</h3>
                <p className="text-slate-400 text-sm">{template.description}</p>
                <div className="flex items-center justify-between mt-3">
                  <span className="text-xs text-slate-500">
                    {template.sections.length} 个部分
                  </span>
                  {template.isDefault && (
                    <span className="text-xs bg-blue-500/20 text-blue-400 px-2 py-1 rounded">
                      默认
                    </span>
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Export Options */}
        <div>
          <label className="block text-sm font-medium text-white mb-3">导出选项</label>
          <div className="space-y-4">
            {/* Format Selection */}
            <div>
              <label className="block text-sm text-slate-400 mb-2">文件格式</label>
              <div className="flex space-x-4">
                {(['pdf', 'excel', 'word', 'html'] as const).map((format) => (
                  <button
                    key={format}
                    type="button"
                    onClick={() => setExportOptions(prev => ({ ...prev, format }))}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                      exportOptions.format === format
                        ? 'bg-blue-600 text-white'
                        : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
                    }`}
                  >
                    {format.toUpperCase()}
                  </button>
                ))}
              </div>
            </div>

            {/* Include Options */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={exportOptions.includeCharts}
                  onChange={(e) => setExportOptions(prev => ({ 
                    ...prev, 
                    includeCharts: e.target.checked 
                  }))}
                  className="rounded border-slate-600 bg-slate-700 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-slate-300">包含图表</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={exportOptions.includeTables}
                  onChange={(e) => setExportOptions(prev => ({ 
                    ...prev, 
                    includeTables: e.target.checked 
                  }))}
                  className="rounded border-slate-600 bg-slate-700 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-slate-300">包含表格</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={exportOptions.includeImages}
                  onChange={(e) => setExportOptions(prev => ({ 
                    ...prev, 
                    includeImages: e.target.checked 
                  }))}
                  className="rounded border-slate-600 bg-slate-700 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-slate-300">包含图片</span>
              </label>
            </div>
          </div>
        </div>

        {/* Generation Status */}
        {generationStatus.status !== 'idle' && (
          <div className="bg-slate-700/50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-medium text-white">报告生成状态</span>
              <div className="flex items-center space-x-2">
                {generationStatus.status === 'generating' && (
                  <RefreshCw className="w-4 h-4 text-blue-400 animate-spin" />
                )}
                {generationStatus.status === 'completed' && (
                  <CheckCircle className="w-4 h-4 text-green-400" />
                )}
                {generationStatus.status === 'failed' && (
                  <AlertCircle className="w-4 h-4 text-red-400" />
                )}
                <span className="text-sm text-slate-300">
                  {generationStatus.status === 'generating' && '生成中...'}
                  {generationStatus.status === 'completed' && '已完成'}
                  {generationStatus.status === 'failed' && '生成失败'}
                </span>
              </div>
            </div>
            
            <div className="w-full bg-slate-600 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${generationStatus.progress}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-4 border-t border-slate-700/50">
          <button
            type="button"
            onClick={handlePreview}
            disabled={!selectedTemplate || loading}
            className="flex items-center space-x-2 px-4 py-2 bg-slate-700 hover:bg-slate-600 disabled:bg-slate-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors duration-200"
          >
            <Eye className="w-4 h-4" />
            <span>预览</span>
          </button>

          <div className="flex items-center space-x-3">
            {generationStatus.status === 'completed' && (
              <button
                type="button"
                onClick={handleDownload}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200"
              >
                <Download className="w-4 h-4" />
                <span>下载报告</span>
              </button>
            )}

            <button
              type="button"
              onClick={handleGenerateReport}
              disabled={!selectedTemplate || !enterpriseId || loading || generationStatus.status === 'generating'}
              className="flex items-center space-x-2 px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-slate-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors duration-200"
            >
              {loading || generationStatus.status === 'generating' ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <FileText className="w-4 h-4" />
              )}
              <span>
                {generationStatus.status === 'generating' ? '生成中...' : '生成报告'}
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportGenerator;
