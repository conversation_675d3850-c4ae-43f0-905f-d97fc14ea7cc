import React from 'react';
import { AlertCircle, RefreshCw } from 'lucide-react';

interface ErrorMessageProps {
  message: string;
  onRetry?: () => void;
  className?: string;
  showIcon?: boolean;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({ 
  message, 
  onRetry, 
  className = '',
  showIcon = true 
}) => {
  return (
    <div className={`p-4 bg-red-500/20 border border-red-500/50 rounded-lg ${className}`}>
      <div className="flex items-center space-x-2">
        {showIcon && <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />}
        <p className="text-red-300 text-sm flex-1">{message}</p>
        {onRetry && (
          <button
            type="button"
            onClick={onRetry}
            className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors duration-200 flex items-center space-x-1"
          >
            <RefreshCw className="w-3 h-3" />
            <span>重试</span>
          </button>
        )}
      </div>
    </div>
  );
};

export default ErrorMessage;
